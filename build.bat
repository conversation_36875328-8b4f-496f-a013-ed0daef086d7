@echo off
chcp 65001 >nul
echo 🔨 构建NewsBot统一系统...

:: 清理旧文件
echo 🧹 清理旧的构建文件...
if exist bin\*.exe del /q bin\*.exe
if exist newsbot.exe del /q newsbot.exe

:: 创建bin目录
if not exist "bin" mkdir bin

:: 构建统一程序
echo 🚀 构建NewsBot统一程序 (newsbot.exe)...
go build -o bin\newsbot.exe main.go
if errorlevel 1 (
    echo ❌ NewsBot构建失败
    pause
    exit /b 1
)

:: 复制到根目录
echo 📋 复制到根目录...
copy bin\newsbot.exe newsbot.exe >nul

echo.
echo ✅ 构建完成！
echo.
echo 📁 构建产物：
echo   • bin\newsbot.exe    - NewsBot统一程序
echo   • newsbot.exe        - 根目录副本
echo.
echo 🚀 使用方法：
echo   默认启动：newsbot.exe
echo   指定配置：newsbot.exe -f config/config.yaml
echo   通过API触发爬虫：POST /api/v1/crawler/start
echo   通过API触发生成器：POST /api/v1/generator/start
echo.
echo 🌐 Web界面：
echo   - 主页：http://localhost:8081
echo   - API健康检查：http://localhost:8081/api/v1/health
echo   - 新闻质量监控：http://localhost:8081/api/v1/quality
echo   - 热榜监控：http://localhost:8081/api/v1/hotlist
echo   - 新闻爬虫API：http://localhost:8081/api/v1/crawler
echo   - 热榜生成API：http://localhost:8081/api/v1/generator
echo   - API测试界面：test_api.html
echo.
echo 📖 详细使用说明请查看 README.md
pause
