@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    NewsBot 开发环境启动脚本
echo ========================================
echo.

:: 检查Go是否安装
go version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未检测到 Go
    echo 请先安装 Go (https://golang.org/)
    pause
    exit /b 1
)

echo ✅ Go 版本:
go version

:: 检查Node.js是否安装
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未检测到 Node.js
    echo 请先安装 Node.js (https://nodejs.org/)
    pause
    exit /b 1
)

echo ✅ Node.js 版本:
node --version
echo.

:: 检查前端依赖
if not exist "frontend\node_modules" (
    echo 📦 正在安装前端依赖...
    cd frontend
    npm install
    if errorlevel 1 (
        echo ❌ 前端依赖安装失败
        pause
        exit /b 1
    )
    cd ..
    echo ✅ 前端依赖安装完成
    echo.
)

:: 构建Go后端
echo 🔨 正在构建后端...
go build -o newsbot.exe .
if errorlevel 1 (
    echo ❌ 后端构建失败
    pause
    exit /b 1
)
echo ✅ 后端构建完成
echo.

echo 🚀 启动开发环境...
echo.
echo 📋 服务信息:
echo   后端服务: http://localhost:8081
echo   前端开发服务: http://localhost:3000
echo   API文档: http://localhost:8081/api/v1/health
echo.
echo 💡 开发提示:
echo   - 后端代码修改后需要重新运行此脚本
echo   - 前端代码修改会自动热重载
echo   - 按 Ctrl+C 停止服务
echo.

:: 启动后端服务（后台运行）
echo 🔧 启动后端服务...
start /b newsbot.exe -f etc/config.yaml

:: 等待后端启动
timeout /t 3 /nobreak >nul

:: 启动前端开发服务
echo 🎨 启动前端开发服务...
cd frontend
npm run dev

:: 清理
echo.
echo 🧹 清理进程...
taskkill /f /im newsbot.exe >nul 2>&1
