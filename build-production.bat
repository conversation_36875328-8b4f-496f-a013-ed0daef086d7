@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    NewsBot 生产环境构建脚本
echo ========================================
echo.

:: 检查Go是否安装
go version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未检测到 Go
    pause
    exit /b 1
)

:: 检查Node.js是否安装
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未检测到 Node.js
    pause
    exit /b 1
)

echo ✅ 环境检查通过
echo.

:: 清理旧的构建文件
echo 🧹 清理旧的构建文件...
if exist "newsbot.exe" del "newsbot.exe"
if exist "frontend\dist" rmdir /s /q "frontend\dist"
echo.

:: 安装前端依赖
if not exist "frontend\node_modules" (
    echo 📦 安装前端依赖...
    cd frontend
    npm install
    if errorlevel 1 (
        echo ❌ 前端依赖安装失败
        pause
        exit /b 1
    )
    cd ..
)

:: 构建前端
echo 🎨 构建前端项目...
cd frontend
npm run build
if errorlevel 1 (
    echo ❌ 前端构建失败
    pause
    exit /b 1
)
cd ..
echo ✅ 前端构建完成
echo.

:: 构建后端
echo 🔨 构建后端项目...
set CGO_ENABLED=1
set GOOS=windows
set GOARCH=amd64
go build -ldflags="-s -w" -o newsbot.exe .
if errorlevel 1 (
    echo ❌ 后端构建失败
    pause
    exit /b 1
)
echo ✅ 后端构建完成
echo.

:: 创建部署目录
echo 📁 创建部署包...
if not exist "deploy" mkdir deploy
if exist "deploy\*" del /q "deploy\*"
if exist "deploy\frontend" rmdir /s /q "deploy\frontend"
if exist "deploy\etc" rmdir /s /q "deploy\etc"
if exist "deploy\data" rmdir /s /q "deploy\data"

:: 复制文件到部署目录
copy "newsbot.exe" "deploy\"
xcopy "frontend\dist" "deploy\frontend\dist\" /e /i /q
xcopy "etc" "deploy\etc\" /e /i /q
if exist "data" xcopy "data" "deploy\data\" /e /i /q
if exist "web" xcopy "web" "deploy\web\" /e /i /q

:: 创建启动脚本
echo @echo off > "deploy\start.bat"
echo echo Starting NewsBot... >> "deploy\start.bat"
echo newsbot.exe -f etc\config.yaml >> "deploy\start.bat"
echo pause >> "deploy\start.bat"

echo.
echo ✅ 构建完成！
echo.
echo 📁 部署文件位置: deploy\
echo 📋 部署包内容:
echo   • newsbot.exe          - 主程序
echo   • frontend\dist\       - 前端静态文件
echo   • etc\                 - 配置文件
echo   • web\                 - 备用前端页面
echo   • start.bat            - 启动脚本
echo.
echo 🚀 部署说明:
echo   1. 将 deploy 目录复制到服务器
echo   2. 修改 etc\config.yaml 配置文件
echo   3. 运行 start.bat 启动服务
echo   4. 访问 http://localhost:8081
echo.
echo 💡 注意事项:
echo   - 确保服务器已安装必要的运行时环境
echo   - 检查防火墙和端口配置
echo   - 建议使用反向代理(Nginx/Apache)
echo.
pause
