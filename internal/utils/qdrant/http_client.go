package qdrant

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"newsBot/internal/types"
	"time"

	"newsBot/internal/config"
)

// HTTPClient Qdrant HTTP客户端
type HTTPClient struct {
	baseURL    string
	httpClient *http.Client
}

// NewHTTPClient 创建新的Qdrant HTTP客户端
func NewHTTPClient() (*HTTPClient, error) {
	cfg := config.GetConfig()
	if cfg == nil {
		return nil, fmt.Errorf("配置加载失败")
	}

	host := cfg.Qdrant.Host
	if host == "" {
		host = "localhost"
	}

	port := cfg.Qdrant.Port
	if port == 0 {
		port = 6333 // HTTP端口
	}

	baseURL := fmt.Sprintf("http://%s:%d", host, port)
	log.Printf("连接到Qdrant HTTP API: %s", baseURL)

	client := &HTTPClient{
		baseURL: baseURL,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}

	// 测试连接
	if err := client.ping(); err != nil {
		return nil, fmt.Errorf("Qdrant连接测试失败: %v", err)
	}

	log.Println("Qdrant HTTP客户端连接成功")
	return client, nil
}

// ping 测试连接
func (c *HTTPClient) ping() error {
	resp, err := c.httpClient.Get(c.baseURL)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return fmt.Errorf("HTTP状态码: %d", resp.StatusCode)
	}

	return nil
}

// CreateCollection 创建集合
func (c *HTTPClient) CreateCollection(collectionName string, vectorSize int) error {
	// 检查集合是否已存在
	exists, err := c.CollectionExists(collectionName)
	if err != nil {
		return fmt.Errorf("检查集合存在性失败: %v", err)
	}

	if exists {
		log.Printf("集合 %s 已存在，跳过创建", collectionName)
		return nil
	}

	// 创建集合的请求体
	createRequest := map[string]interface{}{
		"vectors": map[string]interface{}{
			"size":     vectorSize,
			"distance": "Cosine",
		},
	}

	jsonData, err := json.Marshal(createRequest)
	if err != nil {
		return fmt.Errorf("序列化请求失败: %v", err)
	}

	url := fmt.Sprintf("%s/collections/%s", c.baseURL, collectionName)
	req, err := http.NewRequest("PUT", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 && resp.StatusCode != 201 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("创建集合失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	log.Printf("集合 %s 创建成功，向量维度: %d", collectionName, vectorSize)
	return nil
}

// CollectionExists 检查集合是否存在
func (c *HTTPClient) CollectionExists(collectionName string) (bool, error) {
	url := fmt.Sprintf("%s/collections/%s", c.baseURL, collectionName)
	resp, err := c.httpClient.Get(url)
	if err != nil {
		return false, err
	}
	defer resp.Body.Close()

	return resp.StatusCode == 200, nil
}

// StoreNews 存储单条新闻向量
func (c *HTTPClient) StoreNews(item types.NewsItem, vector []float32) error {
	cfg := config.GetConfig()
	if cfg == nil {
		return fmt.Errorf("配置加载失败")
	}

	collectionName := cfg.Qdrant.CollectionName
	if collectionName == "" {
		collectionName = "news_vectors"
	}

	// 构建点数据
	point := map[string]interface{}{
		"id":     time.Now().UnixNano() % 1000000000, // 使用较小的整数ID
		"vector": vector,
		"payload": map[string]interface{}{
			"title":        item.Title,
			"url":          item.URL,
			"source":       item.Source,
			"content":      item.Content,
			"summary":      item.Summary,
			"author":       item.Author,
			"publish_time": item.PublishTime,
			"timestamp":    item.Timestamp,
			"word_count":   item.WordCount,
			"created_at":   time.Now().Format(time.RFC3339),
		},
	}

	// 构建请求
	request := map[string]interface{}{
		"points": []interface{}{point},
	}

	jsonData, err := json.Marshal(request)
	if err != nil {
		return fmt.Errorf("序列化请求失败: %v", err)
	}

	url := fmt.Sprintf("%s/collections/%s/points", c.baseURL, collectionName)
	req, err := http.NewRequest("PUT", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 && resp.StatusCode != 201 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("存储新闻向量失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	log.Printf("新闻向量存储成功: %s", item.Title)
	return nil
}

// SearchSimilar 搜索相似新闻
func (c *HTTPClient) SearchSimilar(vector []float32, limit int) ([]types.NewsItem, error) {
	cfg := config.GetConfig()
	if cfg == nil {
		return nil, fmt.Errorf("配置加载失败")
	}

	collectionName := cfg.Qdrant.CollectionName
	if collectionName == "" {
		collectionName = "news_vectors"
	}

	// 构建搜索请求
	searchRequest := map[string]interface{}{
		"vector":          vector,
		"limit":           limit,
		"with_payload":    true,
		"score_threshold": 0.2,
	}

	jsonData, err := json.Marshal(searchRequest)
	if err != nil {
		return nil, fmt.Errorf("序列化搜索请求失败: %v", err)
	}

	url := fmt.Sprintf("%s/collections/%s/points/search", c.baseURL, collectionName)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建搜索请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送搜索请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("搜索失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var searchResponse struct {
		Result []struct {
			ID      interface{}            `json:"id"`
			Score   float64                `json:"score"`
			Payload map[string]interface{} `json:"payload"`
		} `json:"result"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&searchResponse); err != nil {
		return nil, fmt.Errorf("解析搜索响应失败: %v", err)
	}

	// 转换结果
	var results []types.NewsItem
	for _, point := range searchResponse.Result {
		payload := point.Payload

		newsItem := types.NewsItem{
			Title:       getStringFromPayload(payload, "title"),
			URL:         getStringFromPayload(payload, "url"),
			Source:      getStringFromPayload(payload, "source"),
			Content:     getStringFromPayload(payload, "content"),
			Summary:     getStringFromPayload(payload, "summary"),
			Author:      getStringFromPayload(payload, "author"),
			PublishTime: getStringFromPayload(payload, "publish_time"),
			Timestamp:   getIntFromPayload(payload, "timestamp"),
			WordCount:   int(getIntFromPayload(payload, "word_count")),
		}

		results = append(results, newsItem)
	}

	log.Printf("相似新闻搜索完成，找到 %d 条相似新闻", len(results))
	return results, nil
}

// GetCollectionStats 获取集合统计信息
func (c *HTTPClient) GetCollectionStats(collectionName string) (map[string]interface{}, error) {
	url := fmt.Sprintf("%s/collections/%s", c.baseURL, collectionName)
	resp, err := c.httpClient.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("获取集合信息失败，状态码: %d", resp.StatusCode)
	}

	var response struct {
		Result struct {
			Status       string `json:"status"`
			PointsCount  int64  `json:"points_count"`
			VectorsCount int64  `json:"vectors_count"`
			Config       struct {
				Params struct {
					VectorSize int    `json:"vector_size"`
					Distance   string `json:"distance"`
				} `json:"params"`
			} `json:"config"`
		} `json:"result"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	stats := map[string]interface{}{
		"status":        response.Result.Status,
		"points_count":  response.Result.PointsCount,
		"vectors_count": response.Result.VectorsCount,
		"vector_size":   response.Result.Config.Params.VectorSize,
		"distance":      response.Result.Config.Params.Distance,
	}

	return stats, nil
}

// InitializeCollection 初始化新闻向量集合
func (c *HTTPClient) InitializeCollection() error {
	cfg := config.GetConfig()
	if cfg == nil {
		return fmt.Errorf("配置加载失败")
	}

	collectionName := cfg.Qdrant.CollectionName
	if collectionName == "" {
		collectionName = "news_vectors"
	}

	vectorSize := cfg.Qdrant.VectorSize
	if vectorSize == 0 {
		vectorSize = 1536 // 通义千问默认维度
	}

	return c.CreateCollection(collectionName, vectorSize)
}

// 辅助函数：从payload中获取字符串值
func getStringFromPayload(payload map[string]interface{}, key string) string {
	if value, exists := payload[key]; exists {
		if str, ok := value.(string); ok {
			return str
		}
	}
	return ""
}

// 辅助函数：从payload中获取整数值
func getIntFromPayload(payload map[string]interface{}, key string) int64 {
	if value, exists := payload[key]; exists {
		switch v := value.(type) {
		case float64:
			return int64(v)
		case int64:
			return v
		case int:
			return int64(v)
		}
	}
	return 0
}
