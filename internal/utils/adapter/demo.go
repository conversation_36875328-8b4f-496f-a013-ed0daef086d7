package adapter

import (
	"fmt"
	"log"
	"newsBot/internal/model"
	"newsBot/internal/types"
	"newsBot/internal/utils/core"
	"newsBot/internal/utils/vector"
	"time"
)

// DemoLLMClient 演示模式LLM客户端
type DemoLLMClient struct{}

func (d *DemoLLMClient) Aggregate(items []core.HotItem) ([]core.Topic, error) {
	// 演示模式：生成模拟话题
	topics := []core.Topic{
		{
			ID:          fmt.Sprintf("demo_topic_%d", time.Now().Unix()),
			Title:       "科技发展趋势",
			Description: "人工智能、区块链等新兴技术的发展动态",
			HotValue:    85.5,
			Categories:  []string{"科技", "创新"},
			CreatedAt:   time.Now(),
		},
		{
			ID:          fmt.Sprintf("demo_topic_%d", time.Now().Unix()+1),
			Title:       "社会热点话题",
			Description: "当前社会关注的重要议题和事件",
			HotValue:    78.2,
			Categories:  []string{"社会", "民生"},
			CreatedAt:   time.Now(),
		},
	}

	log.Printf("🎭 演示模式：生成了 %d 个话题", len(topics))
	return topics, nil
}

func (d *DemoLLMClient) GenerateArticle(topic core.Topic, relatedNews []string) (*core.Article, error) {
	// 演示模式：生成模拟文章
	article := &core.Article{
		Id:        fmt.Sprintf("demo_article_%d", time.Now().Unix()),
		Title:     fmt.Sprintf("深度解读：%s", topic.Title),
		Content:   fmt.Sprintf("这是关于'%s'的深度分析文章。\n\n%s\n\n本文从多个角度分析了相关问题，提供了专业的见解和建议。文章内容丰富，逻辑清晰，适合各类读者阅读。", topic.Title, topic.Description),
		Summary:   fmt.Sprintf("本文深度分析了%s，提供了专业见解。", topic.Title),
		TopicId:   topic.ID,
		WordCount: 150,
		CreatedAt: time.Now(),
		Status:    "draft",
	}

	log.Printf("🎭 演示模式：生成了文章 '%s'", article.Title)
	return article, nil
}

func (d *DemoLLMClient) GenerateSummary(content string) (string, error) {
	return "这是演示模式生成的摘要内容。", nil
}

// DemoNotifier 演示模式通知服务
type DemoNotifier struct{}

func (d *DemoNotifier) SendToEvernote(articles []core.Article) error {
	log.Printf("🎭 演示模式：模拟发送 %d 篇文章到印象笔记", len(articles))
	for _, article := range articles {
		log.Printf("  - %s (%d字)", article.Title, article.WordCount)
	}
	return nil
}

func (d *DemoNotifier) SendEmail(subject, content string, recipients []string) error {
	log.Printf("🎭 演示模式：发送邮件")
	log.Printf("  - 主题: %s", subject)
	log.Printf("  - 收件人: %v", recipients)
	log.Printf("  - 内容长度: %d字符", len(content))
	return nil
}

func (d *DemoNotifier) SendSummaryReport(result core.WorkflowResult) error {
	log.Printf("🎭 演示模式：发送汇总报告")
	log.Printf("  - 话题数量: %d", result.TopicsCount)
	log.Printf("  - 文章数量: %d", result.ArticlesCount)
	log.Printf("  - 执行状态: %v", result.Success)
	log.Printf("  - 执行时间: %s", result.Duration)
	return nil
}

// ContentGenerator 内容生成器实现
type ContentGenerator struct {
	LlmClient core.LLMClient
}

func (c *ContentGenerator) GenerateArticles(topics []core.Topic) ([]core.Article, error) {
	var articles []core.Article

	for _, topic := range topics {
		article, err := c.LlmClient.GenerateArticle(topic, []string{})
		if err != nil {
			log.Printf("⚠️ 话题 '%s' 文章生成失败: %v", topic.Title, err)
			continue
		}
		articles = append(articles, *article)
	}

	return articles, nil
}

func (c *ContentGenerator) GenerateArticle(topic core.Topic, relatedNews []string) (*core.Article, error) {
	return c.LlmClient.GenerateArticle(topic, relatedNews)
}

// NewsRetriever 新闻检索器实现
type NewsRetriever struct {
	VectorService *vector.HTTPService
}

func (n *NewsRetriever) SearchRelatedNews(topic core.Topic, limit int) ([]types.NewsItem, error) {
	if n.VectorService == nil {
		log.Println("🎭 演示模式：跳过向量搜索")
		return []types.NewsItem{}, nil
	}

	// 使用话题标题和描述进行搜索
	searchText := topic.Title + " " + topic.Description
	results, err := n.VectorService.SearchByText(searchText, limit)
	if err != nil {
		return nil, err
	}

	return results, nil
}

// TopicStorageAdapter 话题存储适配器
type TopicStorageAdapter struct {
	Model *model.TopicAggregationModel
}

func (t *TopicStorageAdapter) SaveTopics(topics []core.Topic) error {
	// 这里只是记录话题，实际存储由聚合器处理
	log.Printf("💾 保存 %d 个话题到数据库", len(topics))
	return nil
}

func (t *TopicStorageAdapter) GetTopics(limit int) ([]core.Topic, error) {
	// 返回空列表，因为我们主要用于记录
	return []core.Topic{}, nil
}

func (t *TopicStorageAdapter) GetTopicByID(id string) (*core.Topic, error) {
	return nil, fmt.Errorf("topic not found")
}

func (t *TopicStorageAdapter) UpdateTopic(topic core.Topic) error {
	return nil
}

func (t *TopicStorageAdapter) DeleteTopic(id string) error {
	return nil
}

// ArticleStorageAdapter 文章存储适配器
type ArticleStorageAdapter struct {
	Model *model.GeneratedArticleModel
}

func (a *ArticleStorageAdapter) SaveArticle(article core.Article) error {
	// 保存文章记录到数据库
	// 注意：这里的SessionId设为0，因为这是适配器模式，实际的sessionId在orchestrator中处理
	record := model.GeneratedArticleRecord{
		SessionId:    0, // 这里设为0，实际的sessionId在orchestrator中设置
		TopicId:      article.TopicId,
		TopicTitle:   "Generated Topic",
		ArticleTitle: article.Title,
		ArticleId:    article.Id,
		WordCount:    article.WordCount,
		LLMModel:     "Demo",
		Status:       "success",
	}
	return a.Model.SaveGeneratedArticleRecord(record)
}

func (a *ArticleStorageAdapter) GetArticles(limit int) ([]core.Article, error) {
	return []core.Article{}, nil
}

func (a *ArticleStorageAdapter) GetArticleByID(id string) (*core.Article, error) {
	return nil, fmt.Errorf("article not found")
}

func (a *ArticleStorageAdapter) UpdateArticle(article core.Article) error {
	return nil
}

func (a *ArticleStorageAdapter) DeleteArticle(id string) error {
	return nil
}

func (a *ArticleStorageAdapter) GetArticlesByStatus(status string) ([]core.Article, error) {
	return []core.Article{}, nil
}
