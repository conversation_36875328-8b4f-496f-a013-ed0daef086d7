package crawler

import (
	"encoding/json"
	"log"
	"newsBot/internal/types"
	"newsBot/internal/utils"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	"github.com/go-resty/resty/v2"
)

// CrawlCCTV 爬取央视新闻
func CrawlCCTV() []types.NewsItem {
	var results []types.NewsItem
	client := utils.CreateClient()

	log.Println("开始爬取央视新闻...")

	// 方法1: 使用央视新闻API
	results = append(results, crawlCCTVAPI(client)...)

	// 方法2: 爬取央视网首页
	results = append(results, crawlCCTVHomepage(client)...)

	log.Printf("央视新闻爬取完成，共获取 %d 条新闻", len(results))
	return results
}

// crawlCCTVAPI 使用央视新闻API
func crawlCCTVAPI(client *resty.Client) []types.NewsItem {
	var results []types.NewsItem

	// 计算24小时前的时间戳（暂时未使用，但保留用于将来扩展）
	_ = time.Now().Add(-24*time.Hour).UnixNano() / int64(time.Millisecond)

	// 央视新闻API
	apiURL := "https://api.cntv.cn/NewVideo/getVideoListByColumn"
	resp, err := client.R().
		SetQueryParams(map[string]string{
			"id":        "TOPC1451558856402041", // 新闻频道ID
			"n":         "20",
			"sort":      "desc",
			"p":         "1",
			"mode":      "0",
			"serviceId": "tvcctv",
		}).
		Get(apiURL)

	if err != nil {
		log.Printf("央视新闻API请求失败: %v", err)
		return results
	}

	// 解析API响应
	var apiResult struct {
		Data struct {
			List []struct {
				Title       string `json:"title"`
				URL         string `json:"url"`
				Image       string `json:"image"`
				Time        string `json:"time"`
				Description string `json:"description"`
				Focus_date  string `json:"focus_date"`
			} `json:"list"`
		} `json:"data"`
	}

	if err := json.Unmarshal(resp.Body(), &apiResult); err != nil {
		log.Printf("央视新闻API响应解析失败: %v", err)
		return results
	}

	// 处理API结果
	for _, item := range apiResult.Data.List {
		if item.Title != "" && item.URL != "" {
			newsItem := types.NewsItem{
				Source:    "央视新闻",
				Title:     item.Title,
				URL:       item.URL,
				Timestamp: time.Now().Unix(),
				Content:   item.Description,
			}
			results = append(results, newsItem)
		}
	}

	return results
}

// crawlCCTVHomepage 爬取央视网首页
func crawlCCTVHomepage(client *resty.Client) []types.NewsItem {
	var results []types.NewsItem

	// 尝试多个央视网新闻页面
	urls := []string{
		"https://news.cctv.com/2025/07/",
		"https://news.cctv.com/china/",
		"https://news.cctv.com/world/",
		"https://news.cctv.com/",
	}

	for _, url := range urls {
		resp, err := client.R().Get(url)
		if err != nil {
			log.Printf("央视网 %s 请求失败: %v", url, err)
			continue
		}

		doc, err := goquery.NewDocumentFromReader(strings.NewReader(resp.String()))
		if err != nil {
			log.Printf("央视网 %s 解析失败: %v", url, err)
			continue
		}

		// 查找新闻链接 - 使用更精确的选择器
		selectors := []string{
			".news_list a",
			".list a",
			"h3 a",
			"h4 a",
			".title a",
			"li a",
		}

		for _, selector := range selectors {
			doc.Find(selector).Each(func(i int, s *goquery.Selection) {
				title := strings.TrimSpace(s.Text())
				link, exists := s.Attr("href")

				if !exists || title == "" || len(title) < 15 {
					return
				}

				// 过滤有效的新闻链接，排除直播间和视频
				if (strings.Contains(link, "cctv.com") || strings.HasPrefix(link, "/")) &&
					!strings.Contains(link, "javascript") &&
					!strings.Contains(link, "#") &&
					!strings.Contains(link, "VIDE") && // 排除直播间链接
					!strings.Contains(link, "live") && // 排除直播页面
					!strings.Contains(link, "video") && // 排除视频页面
					strings.Contains(link, "ARTI") && // 只要文章链接
					len(title) > 10 {

					// 确保链接是完整的URL
					if strings.HasPrefix(link, "/") {
						link = "https://news.cctv.com" + link
					}

					newsItem := types.NewsItem{
						Source:    "央视新闻",
						Title:     title,
						URL:       link,
						Timestamp: time.Now().Unix(),
						Content:   "", // 稍后提取完整内容
					}
					results = append(results, newsItem)

					// 限制每个选择器的结果数量
					if len(results) >= 8 {
						return
					}
				}
			})

			if len(results) >= 8 {
				break
			}
		}

		if len(results) >= 8 {
			break
		}
	}

	// 去重
	seen := make(map[string]bool)
	var uniqueResults []types.NewsItem
	for _, item := range results {
		if !seen[item.URL] {
			seen[item.URL] = true
			uniqueResults = append(uniqueResults, item)
		}
	}

	// 限制结果数量
	if len(uniqueResults) > 6 {
		uniqueResults = uniqueResults[:6]
	}

	return uniqueResults
}

// crawlCCTVSearch 搜索央视新闻
func crawlCCTVSearch(client *resty.Client) []types.NewsItem {
	var results []types.NewsItem

	// 央视搜索API
	searchURL := "https://search.cctv.com/search.php"
	_, err := client.R().
		SetQueryParams(map[string]string{
			"qtext":   "热点",
			"sort":    "relevance",
			"type":    "web",
			"vtime":   "",
			"datepid": "1",
			"channel": "新闻",
		}).
		Get(searchURL)

	if err != nil {
		log.Printf("央视搜索请求失败: %v", err)
		return results
	}

	// 这里应该解析搜索结果页面
	// 为了简化，我们返回空结果
	log.Println("央视搜索功能需要进一步实现")

	return results
}
