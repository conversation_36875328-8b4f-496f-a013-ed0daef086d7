package hotlist

import (
	"encoding/json"
	"fmt"
	"log"
	"newsBot/internal/utils/core"
	"newsBot/internal/utils/vectorizer"
	"strings"
	"sync"
	"time"

	"newsBot/internal/model"
	"newsBot/internal/svc"
)

// AggregatorServiceImpl 热榜聚合服务实现
type AggregatorServiceImpl struct {
	crawlers  []core.HotlistCrawler
	llmClient core.LLMClient
	storage   core.TopicStorage
	processor *HotItemProcessor
}

// HotItemProcessor 热榜项目处理器
type HotItemProcessor struct {
	minHotValue float64
	maxItems    int
}

// NewAggregatorService 创建新的聚合服务
func NewAggregatorService(
	crawlers []core.HotlistCrawler,
	llmClient core.LLMClient,
	storage core.TopicStorage,
) *AggregatorServiceImpl {
	return &AggregatorServiceImpl{
		crawlers:  crawlers,
		llmClient: llmClient,
		storage:   storage,
		processor: &HotItemProcessor{
			minHotValue: 1.0,
			maxItems:    100,
		},
	}
}

// AggregateTopics 聚合话题
func (s *AggregatorServiceImpl) AggregateTopics() ([]core.Topic, int64, error) {
	log.Println("🔥 开始聚合热榜话题...")

	// 开始热搜会话
	var sessionID int64
	var err error
	svcCtx := svc.SvcCtx
	if svcCtx != nil {
		sessionID, err = svcCtx.HotlistSessionModel.StartHotlistSession()
		if err != nil {
			log.Printf("创建热搜会话失败: %v", err)
		}
	}

	// 1. 爬取所有热榜
	log.Println("📱 爬取各平台热榜...")
	allItems := s.crawlAllHotlists()
	log.Printf("📊 共爬取到 %d 个热榜项目", len(allItems))

	// 保存热搜记录到数据库
	successCount := 0
	if svcCtx != nil && sessionID > 0 {
		for _, item := range allItems {
			if err := svcCtx.HotlistRecordModel.SaveHotlistRecord(sessionID, item); err != nil {
				log.Printf("保存热搜记录失败: %v", err)
			} else {
				successCount++
			}
		}
		log.Printf("💾 数据库保存: %d 条热搜记录", successCount)
	}

	if len(allItems) == 0 {
		// 结束热搜会话（失败）
		if svcCtx != nil && sessionID > 0 {
			svcCtx.HotlistSessionModel.EndHotlistSession(sessionID, 0, 0, "failed", "未获取到任何热榜数据")
		}
		return nil, sessionID, fmt.Errorf("未获取到任何热榜数据")
	}

	// 2. 清理和预处理
	log.Println("🧹 清理和预处理数据...")
	cleanedItems := s.processor.CleanHotItems(allItems)
	log.Printf("✅ 清理后剩余 %d 个有效项目", len(cleanedItems))

	// 3. LLM聚合
	log.Println("🤖 使用LLM进行话题聚合...")
	startTime := time.Now()
	aggregated, err := s.llmClient.Aggregate(cleanedItems)
	processTime := time.Since(startTime).Seconds()

	// 保存话题合并记录
	if svcCtx != nil && sessionID > 0 {
		outputTopicsJSON, _ := json.Marshal(aggregated)
		record := model.TopicAggregationRecord{
			SessionId:       sessionID,
			InputItemsCount: len(cleanedItems),
			OutputTopics:    string(outputTopicsJSON),
			LLMModel:        s.getLLMModelName(),
			ProcessTime:     processTime,
			Status:          "success",
		}

		if err != nil {
			record.Status = "failed"
			record.ErrorMsg = err.Error()
		}

		if saveErr := svcCtx.TopicAggregationModel.SaveTopicAggregationRecord(record); saveErr != nil {
			log.Printf("保存话题合并记录失败: %v", saveErr)
		} else {
			log.Printf("💾 话题合并记录已保存: %d 个输入项 -> %d 个话题", len(cleanedItems), len(aggregated))
		}
	}

	if err != nil {
		return nil, sessionID, fmt.Errorf("LLM聚合失败: %v", err)
	}
	log.Printf("🎯 聚合生成 %d 个话题，耗时: %.2f秒", len(aggregated), processTime)

	// 4. 向量化
	log.Println("🔢 对话题进行向量化...")
	for i := range aggregated {
		text := aggregated[i].Title + " " + aggregated[i].Description
		vector, err := vectorizer.VectorizeText(text)
		if err != nil {
			log.Printf("⚠️ 话题 '%s' 向量化失败: %v", aggregated[i].Title, err)
			continue
		}
		aggregated[i].Vector = vector
		aggregated[i].CreatedAt = time.Now()
		aggregated[i].UpdatedAt = time.Now()
	}

	// 5. 存储到数据库
	log.Println("💾 保存话题到数据库...")
	if err := s.storage.SaveTopics(aggregated); err != nil {
		// 结束热搜会话（失败）
		if svcCtx != nil && sessionID > 0 {
			svcCtx.HotlistSessionModel.EndHotlistSession(sessionID, len(allItems), successCount, "failed", err.Error())
		}
		return nil, sessionID, fmt.Errorf("话题存储失败: %v", err)
	}

	// 结束热搜会话（成功）
	if svcCtx != nil && sessionID > 0 {
		status := "completed"
		errorMsg := ""
		if successCount < len(allItems) {
			status = "partial"
			errorMsg = fmt.Sprintf("部分保存失败: %d/%d", successCount, len(allItems))
		}

		if err := svcCtx.HotlistSessionModel.EndHotlistSession(sessionID, len(allItems), successCount, status, errorMsg); err != nil {
			log.Printf("结束热搜会话失败: %v", err)
		}
	}

	log.Printf("🎉 话题聚合完成! 共生成 %d 个高质量话题", len(aggregated))
	return aggregated, sessionID, nil
}

// GetLatestTopics 获取最新话题
func (s *AggregatorServiceImpl) GetLatestTopics(limit int) ([]core.Topic, error) {
	return s.storage.GetTopics(limit)
}

// GetTopicsByCategory 按分类获取话题
func (s *AggregatorServiceImpl) GetTopicsByCategory(category string, limit int) ([]core.Topic, error) {
	// 获取所有话题，然后过滤
	allTopics, err := s.storage.GetTopics(limit * 2) // 获取更多以便过滤
	if err != nil {
		return nil, err
	}

	var filteredTopics []core.Topic
	for _, topic := range allTopics {
		for _, cat := range topic.Categories {
			if cat == category {
				filteredTopics = append(filteredTopics, topic)
				break
			}
		}
		if len(filteredTopics) >= limit {
			break
		}
	}

	return filteredTopics, nil
}

// getLLMModelName 获取LLM模型名称
func (s *AggregatorServiceImpl) getLLMModelName() string {
	// 根据LLM客户端类型返回模型名称
	// 由于接口类型断言的限制，这里返回通用名称
	return "LLM"
}

// crawlAllHotlists 并发爬取所有热榜
func (s *AggregatorServiceImpl) crawlAllHotlists() []core.HotItem {
	var wg sync.WaitGroup
	results := make(chan []core.HotItem, len(s.crawlers))

	// 启动所有爬虫
	for _, crawler := range s.crawlers {
		if !crawler.IsEnabled() {
			continue
		}

		wg.Add(1)
		go func(c core.HotlistCrawler) {
			defer wg.Done()

			log.Printf("🕷️ 开始爬取 %s 热榜...", c.Platform())
			items, err := c.Crawl()
			if err != nil {
				log.Printf("❌ %s 热榜爬取失败: %v", c.Platform(), err)
				return
			}

			log.Printf("✅ %s 热榜爬取成功，获取 %d 个项目", c.Platform(), len(items))
			results <- items
		}(crawler)
	}

	// 等待所有爬虫完成
	go func() {
		wg.Wait()
		close(results)
	}()

	// 收集所有结果
	var allItems []core.HotItem
	for items := range results {
		allItems = append(allItems, items...)
	}

	return allItems
}

// CleanHotItems 清理热榜项目
func (p *HotItemProcessor) CleanHotItems(items []core.HotItem) []core.HotItem {
	var cleaned []core.HotItem
	seen := make(map[string]bool)

	for _, item := range items {
		// 去重（基于标题）
		if seen[item.Title] {
			continue
		}
		seen[item.Title] = true

		// 过滤低热度项目
		if item.HotValue < p.minHotValue {
			continue
		}

		// 过滤无效标题
		if len(item.Title) < 5 || len(item.Title) > 200 {
			continue
		}

		// 过滤广告和垃圾内容
		if p.isSpamContent(item.Title) {
			continue
		}

		cleaned = append(cleaned, item)
	}

	// 按热度排序并限制数量
	if len(cleaned) > p.maxItems {
		// 简单排序（实际应该按HotValue排序）
		cleaned = cleaned[:p.maxItems]
	}

	return cleaned
}

// isSpamContent 检查是否为垃圾内容
func (p *HotItemProcessor) isSpamContent(title string) bool {
	spamKeywords := []string{
		"广告", "推广", "营销", "代理", "加盟",
		"微商", "刷单", "兼职", "赚钱", "投资理财",
	}

	for _, keyword := range spamKeywords {
		if strings.Contains(title, keyword) {
			return true
		}
	}

	return false
}
