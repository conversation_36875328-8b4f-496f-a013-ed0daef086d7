package core

import (
	"newsBot/internal/types"
	"time"
)

// 使用types包中的类型定义
type Topic = types.Topic
type Article = types.Article
type HotItem = types.HotItem

// WorkflowResult 工作流执行结果
type WorkflowResult struct {
	TopicsCount   int       `json:"topics_count"`
	ArticlesCount int       `json:"articles_count"`
	Success       bool      `json:"success"`
	Error         string    `json:"error,omitempty"`
	Duration      string    `json:"duration"`
	ExecutedAt    time.Time `json:"executed_at"`
}

// ContentGenerator 内容生成器接口
type ContentGenerator interface {
	GenerateArticles(topics []Topic) ([]Article, error)
	GenerateArticle(topic Topic, relatedNews []string) (*Article, error)
}

// TopicAggregator 话题聚合器接口
type TopicAggregator interface {
	AggregateTopics() ([]Topic, int64, error) // 返回话题列表和会话ID
	GetLatestTopics(limit int) ([]Topic, error)
	GetTopicsByCategory(category string, limit int) ([]Topic, error)
}

// HotlistCrawler 热榜爬虫接口
type HotlistCrawler interface {
	Crawl() ([]HotItem, error)
	Platform() string
	IsEnabled() bool
}

// Notifier 通知器接口
type Notifier interface {
	SendToEvernote(articles []Article) error
	SendEmail(subject, content string, recipients []string) error
	SendSummaryReport(result WorkflowResult) error
}

// LLMClient LLM客户端接口
type LLMClient interface {
	Aggregate(items []HotItem) ([]Topic, error)
	GenerateArticle(topic Topic, relatedNews []string) (*Article, error)
	GenerateSummary(content string) (string, error)
}

// TopicStorage 话题存储接口
type TopicStorage interface {
	SaveTopics(topics []Topic) error
	GetTopics(limit int) ([]Topic, error)
	GetTopicByID(id string) (*Topic, error)
	UpdateTopic(topic Topic) error
	DeleteTopic(id string) error
}

// ArticleStorage 文章存储接口
type ArticleStorage interface {
	SaveArticle(article Article) error
	GetArticles(limit int) ([]Article, error)
	GetArticleByID(id string) (*Article, error)
	UpdateArticle(article Article) error
	DeleteArticle(id string) error
	GetArticlesByStatus(status string) ([]Article, error)
}
