package router

import (
	"newsBot/internal/handler"
	"newsBot/internal/middleware"
	"newsBot/internal/svc"
	"os"
	"strings"

	"github.com/gin-gonic/gin"
)

// SetupRouter 设置路由
func SetupRouter(r *gin.Engine, svcCtx *svc.ServiceContext) {
	// 添加中间件
	r.Use(middleware.Logger())
	r.Use(middleware.Recovery())
	r.Use(middleware.CORS())

	// 创建处理器（所有handler都从服务上下文中获取向量服务）
	healthHandler := handler.NewHealthHandler(svcCtx)
	qualityHandler := handler.NewQualityHandler(svcCtx)
	hotlistHandler := handler.NewHotlistHandler(svcCtx)
	llmHandler := handler.NewLLMHandler(svcCtx)
	searchHandler := handler.NewSearchHandler(svcCtx)
	crawlerHandler := handler.NewCrawlerHandler(svcCtx)
	hotlistGeneratorHandler := handler.NewHotlistGeneratorHandler(svcCtx)

	// 静态文件服务
	// 优先检查是否有Vue构建的前端项目
	if _, err := os.Stat("./frontend/dist/index.html"); err == nil {
		// 如果存在Vue构建的前端，使用它
		r.Static("/assets", "./frontend/dist/assets")
		r.StaticFile("/favicon.ico", "./frontend/dist/favicon.ico")
		r.NoRoute(func(c *gin.Context) {
			// 对于前端路由，返回index.html
			if !strings.HasPrefix(c.Request.URL.Path, "/api") {
				c.File("./frontend/dist/index.html")
			} else {
				c.JSON(404, gin.H{"error": "API endpoint not found"})
			}
		})
	} else {
		// 否则使用原来的简单HTML页面
		r.Static("/static", "./web")
		r.StaticFile("/", "./web/index.html")
	}

	// API路由组
	api := r.Group("/api/v1")
	{
		// 健康检查
		api.GET("/health", healthHandler.HealthCheck)
		api.GET("/stats", healthHandler.Stats)

		// 搜索相关
		search := api.Group("/search")
		{
			search.GET("/text", searchHandler.SearchByText)
			search.POST("/similar", searchHandler.SearchSimilar)
		}

		// 质量监控相关
		quality := api.Group("/quality")
		{
			quality.GET("/sessions", qualityHandler.GetSessions)
			quality.GET("/news", qualityHandler.GetNews)
			quality.GET("/stats", qualityHandler.GetStats)
			quality.GET("/detail", qualityHandler.GetDetail)
		}

		// 热榜相关
		hotlist := api.Group("/hotlist")
		{
			hotlist.GET("/sessions", hotlistHandler.GetSessions)
			hotlist.GET("/records", hotlistHandler.GetRecords)
			hotlist.GET("/stats", hotlistHandler.GetStats)
		}

		// LLM处理相关
		llm := api.Group("/llm")
		{
			llm.GET("/topic-aggregation", llmHandler.GetTopicAggregationRecords)
			llm.GET("/vector-search", llmHandler.GetVectorSearchRecords)
			llm.GET("/generated-articles", llmHandler.GetGeneratedArticleRecords)
			llm.GET("/topic-aggregation/session", llmHandler.GetTopicAggregationBySession)
			llm.GET("/vector-search/topic", llmHandler.GetVectorSearchByTopic)
			llm.GET("/generated-articles/topic", llmHandler.GetGeneratedArticlesByTopic)
			llm.GET("/search/results", llmHandler.GetSearchResults)
			llm.GET("/generated-articles/detail", llmHandler.GetGeneratedArticleDetail)
		}

		// 新闻爬虫相关
		crawler := api.Group("/crawler")
		{
			crawler.POST("/start", crawlerHandler.StartCrawl)
			crawler.GET("/status", crawlerHandler.GetCrawlStatus)
			crawler.GET("/health", crawlerHandler.HealthCheck)
			crawler.DELETE("/clean", crawlerHandler.CleanOldNews)
		}

		// 热榜生成相关
		generator := api.Group("/generator")
		{
			generator.POST("/start", hotlistGeneratorHandler.StartGeneration)
			generator.GET("/status", hotlistGeneratorHandler.GetGenerationStatus)
			generator.GET("/health", hotlistGeneratorHandler.HealthCheck)
		}
	}
}
