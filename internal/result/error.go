package result

import "errors"

// 业务错误定义
var (
	ErrInvalidParam    = errors.New("参数错误")
	ErrDatabaseError   = errors.New("数据库错误")
	ErrServiceNotAvail = errors.New("服务不可用")
	ErrRecordNotFound  = errors.New("记录不存在")
	ErrVectorService   = errors.New("向量服务错误")
	ErrLLMService      = errors.New("LLM服务错误")
	ErrCrawlerService  = errors.New("爬虫服务错误")
	ErrEmailService    = errors.New("邮件服务错误")
)

// BusinessError 业务错误结构
type BusinessError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

func (e BusinessError) Error() string {
	return e.Message
}

// NewBusinessError 创建业务错误
func NewBusinessError(code int, message string) *BusinessError {
	return &BusinessError{
		Code:    code,
		Message: message,
	}
}
