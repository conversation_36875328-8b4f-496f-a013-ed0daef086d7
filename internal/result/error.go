package result

var (
	ErrorReqParam         = ErrorResult(10000, "请求参数错误")
	ErrorBindingParam     = ErrorResult(10001, "绑定参数错误")
	ErrorAdd              = ErrorResult(10100, "添加失败")
	ErrorUpdate           = ErrorResult(10101, "更新失败")
	ErrorDelete           = ErrorResult(10102, "删除失败")
	ErrorSelect           = ErrorResult(10103, "查询失败")
	ErrorAnalyze          = ErrorResult(10104, "分析失败")
	ErrorExportExcel      = ErrorResult(10105, "导出excel失败")
	ErrorCopy             = ErrorResult(10106, "复制失败")
	ErrorSubDirExist      = ErrorResult(10107, "子目录已存在")
	ErrorToken            = ErrorResult(10108, "生成token失败")
	ErrorUserLogin        = ErrorResult(10109, "用户名或密码错误")
	ErrorNotFound         = ErrorResult(10110, "未查询到数据")
	ErrorUserInfo         = ErrorResult(10111, "获取用户信息失败")
	ErrorGeneratePassword = ErrorResult(10112, "生成密码失败")
	ErrorTxCommit         = ErrorResult(10113, "事务提交失败")
	ErrorDataVerify       = ErrorResult(10114, "数据校验失败")
	ErrorDisableLogin     = ErrorResult(10115, "用户已禁用")
	ErrorServerError      = ErrorResult(10116, "服务器内部错误")
	ErrorServiceNotAvail  = ErrorResult(10117, "服务不可用")
	ErrorVectorService    = ErrorResult(10118, "向量服务错误")
	ErrorCrawlerService   = ErrorResult(10119, "爬虫服务错误")
	ErrorLLMService       = ErrorResult(10120, "LLM服务错误")
)

func ErrorResult(code int, msg string) *Result {
	return &Result{
		Code: code,
		Msg:  msg,
	}
}

func ErrorSimpleResult(msg string) *Result {
	return &Result{
		Code: 20000,
		Msg:  msg,
	}
}
