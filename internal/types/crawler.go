package types

// StartCrawlRequest 启动爬取请求
type StartCrawlRequest struct {
	EnableVector bool `json:"enable_vector" form:"enable_vector"` // 是否启用向量化
	DemoMode     bool `json:"demo_mode" form:"demo_mode"`         // 演示模式
}

// CrawlResponse 爬取响应
type CrawlResponse struct {
	SessionID    int64                  `json:"session_id"`
	TotalNews    int                    `json:"total_news"`
	SuccessCount int                    `json:"success_count"`
	SourceStats  map[string]int         `json:"source_stats"`
	ContentCount int                    `json:"content_count"`
	CoverageRate float64                `json:"coverage_rate"`
	Duration     string                 `json:"duration"`
	VectorStats  map[string]interface{} `json:"vector_stats,omitempty"`
}
