package types

// StartGenerationRequest 启动生成请求
type StartGenerationRequest struct {
	DemoMode bool `json:"demo_mode" form:"demo_mode"` // 演示模式
}

// GenerationResponse 生成响应
type GenerationResponse struct {
	SessionID     int64  `json:"session_id"`
	TopicsCount   int    `json:"topics_count"`
	ArticlesCount int    `json:"articles_count"`
	Duration      string `json:"duration"`
	Success       bool   `json:"success"`
	Error         string `json:"error,omitempty"`
}
