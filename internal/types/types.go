package types

import (
	"time"
)

type StreamResponse struct {
	Id      string   `json:"id"`
	Object  string   `json:"object"`
	Created int64    `json:"created"`
	Model   string   `json:"model"`
	Choices []Choice `json:"choices"`
	Usage   *Usage   `json:"usage"` // 可能为 null
}

type Choice struct {
	Index        int     `json:"index"`
	Delta        Delta   `json:"delta"`
	FinishReason *string `json:"finish_reason"` // 可能为 null 或 "stop"
}

type Delta struct {
	Role    string `json:"role,omitempty"`
	Content string `json:"content,omitempty"`
}

type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	TotalTokens      int `json:"total_tokens"`
	CompletionTokens int `json:"completion_tokens"`
}

// 新建阿里百练请求结构体
// AliDeepSeekResponse 阿里云百炼平台的响应结构体
type AliDeepSeekResponse struct {
	Choices []struct {
		Delta struct {
			Content          string `json:"content,omitempty"`
			ReasoningContent string `json:"reasoning_content"`
		} `json:"delta"`
		FinishReason string  `json:"finish_reason,omitempty"`
		Index        int     `json:"index"`
		Logprobs     float64 `json:"logprobs,omitempty"`
	} `json:"choices"`
	Object            string `json:"object"`
	Usage             Usage  `json:"usage,omitempty"`
	Created           int64  `json:"created"`
	SystemFingerprint string `json:"system_fingerprint,omitempty"`
	Model             string `json:"model"`
	Id                string `json:"id"`
}

// openRouter请求结构体
// OpenRouterResponse openRouter的响应结构体
type OpenRouterResponse struct {
	ID       string       `json:"id"`
	Provider string       `json:"provider"`
	Model    string       `json:"model"`
	Object   string       `json:"object"`
	Created  int64        `json:"created"`
	Choices  []OpenChoice `json:"choices"`
}

type OpenChoice struct {
	Index              int       `json:"index"`
	Delta              OpenDelta `json:"delta"`
	FinishReason       *string   `json:"finish_reason,omitempty"`
	NativeFinishReason *string   `json:"native_finish_reason,omitempty"`
	Logprobs           *float64  `json:"logprobs,omitempty"`
}

type OpenDelta struct {
	Role      string `json:"role"`
	Content   string `json:"content"`
	Reasoning string `json:"reasoning"`
}

// HistoryMessage 历史消息内容结构体
type HistoryMessage struct {
	Role    string `json:"role"`    // 消息角色
	Content string `json:"content"` // 消息内容
}

type ApiConfig struct {
	Url       string `json:"url"`
	SecretKey string `json:"secretKey"`
}

// NewsItem 新闻项数据结构
type NewsItem struct {
	Source      string `json:"source"`       // 来源
	Title       string `json:"title"`        // 标题
	URL         string `json:"url"`          // 链接
	Timestamp   int64  `json:"timestamp"`    // 时间戳
	Content     string `json:"content"`      // 完整新闻内容
	Summary     string `json:"summary"`      // 内容摘要
	Author      string `json:"author"`       // 作者
	PublishTime string `json:"publish_time"` // 发布时间
	WordCount   int    `json:"word_count"`   // 字数统计
}

// HotItem 热榜项目
type HotItem struct {
	Title       string    `json:"title"`
	Description string    `json:"description"`
	URL         string    `json:"url"`
	HotValue    float64   `json:"hot_value"`
	Platform    string    `json:"platform"`
	Rank        int       `json:"rank"`
	CreatedAt   time.Time `json:"created_at"`
}

// Topic 话题结构
type Topic struct {
	ID          string    `json:"id"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	HotValue    float64   `json:"hot_value"`
	Categories  []string  `json:"categories"`
	Platform    string    `json:"platform"`
	Vector      []float32 `json:"-"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// Article 生成的文章结构
type Article struct {
	Id          string    `json:"id"`
	Title       string    `json:"title"`
	Content     string    `json:"content"`
	Summary     string    `json:"summary"`
	TopicId     string    `json:"topic_id"`
	RelatedNews []string  `json:"related_news"` // 相关新闻URL列表
	WordCount   int       `json:"word_count"`
	CreatedAt   time.Time `json:"created_at"`
	Status      string    `json:"status"` // draft, reviewed, published
}
