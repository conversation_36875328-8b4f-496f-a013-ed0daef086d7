package handler

import (
	"newsBot/internal/result"
	"newsBot/internal/svc"
	"time"

	"github.com/gin-gonic/gin"
)

// HealthHandler 健康检查处理器
type HealthHandler struct {
	svcCtx *svc.ServiceContext
}

// NewHealthHandler 创建健康检查处理器
func NewHealthHandler(svcCtx *svc.ServiceContext) *HealthHandler {
	return &HealthHandler{
		svcCtx: svcCtx,
	}
}

// HealthCheck 健康检查
func (h *HealthHandler) HealthCheck(c *gin.Context) {
	response := map[string]interface{}{
		"status":    "ok",
		"timestamp": time.Now().Unix(),
	}

	// 检查向量服务健康状态
	if h.svcCtx.VectorService != nil {
		if err := h.svcCtx.VectorService.HealthCheck(); err != nil {
			response["vector_service"] = "error: " + err.Error()
		} else {
			response["vector_service"] = "ok"
		}
	} else {
		response["vector_service"] = "disabled"
	}

	// 检查数据库状态
	if h.svcCtx != nil && h.svcCtx.DB != nil {
		response["database"] = "ok"
	} else {
		response["database"] = "disabled"
	}

	result.Success(c, response)
}

// Stats 统计信息
func (h *HealthHandler) Stats(c *gin.Context) {
	response := map[string]interface{}{
		"timestamp": time.Now().Unix(),
	}

	// 获取向量统计信息（如果可用）
	if h.svcCtx.VectorService != nil {
		stats, err := h.svcCtx.VectorService.GetCollectionStats()
		if err != nil {
			response["vector_stats"] = "error: " + err.Error()
		} else {
			response["vector_dimension"] = h.svcCtx.VectorService.GetVectorDimension()
			response["collection_stats"] = stats
		}
	} else {
		response["vector_stats"] = "disabled"
	}

	result.Success(c, response)
}
