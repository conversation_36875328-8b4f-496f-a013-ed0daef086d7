package handler

import (
	"log"
	"newsBot/internal/result"
	"newsBot/internal/svc"
	"newsBot/internal/types"
	"strconv"

	"github.com/gin-gonic/gin"
)

// SearchHandler 搜索处理器
type SearchHandler struct {
	svcCtx *svc.ServiceContext
}

// NewSearchHandler 创建搜索处理器
func NewSearchHandler(svcCtx *svc.ServiceContext) *SearchHandler {
	return &SearchHandler{
		svcCtx: svcCtx,
	}
}

// SearchByText 文本搜索
func (h *SearchHandler) SearchByText(c *gin.Context) {
	// 获取查询参数
	text := c.Query("text")
	if text == "" {
		c.JSON(200, result.ErrorReqParam)
		return
	}

	limitStr := c.<PERSON>("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 100 {
		limit = 10
	}

	// 检查向量服务是否可用
	if h.svcCtx.VectorService == nil {
		c.JSO<PERSON>(200, result.ErrorVectorService)
		return
	}

	// 执行搜索
	results, err := h.svcCtx.VectorService.SearchByText(text, limit)
	if err != nil {
		log.Printf("搜索失败: %v", err)
		c.JSON(200, result.ErrorVectorService.AddError(err))
		return
	}

	// 返回结果
	response := map[string]interface{}{
		"query":   text,
		"limit":   limit,
		"results": results,
		"count":   len(results),
	}

	c.JSON(200, result.SuccessResult(response))
}

// SearchSimilar 相似搜索
func (h *SearchHandler) SearchSimilar(c *gin.Context) {
	// 解析请求
	var request types.SimilarRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		log.Printf("解析请求失败: %v", err)
		c.JSON(200, result.ErrorBindingParam.AddError(err))
		return
	}

	// 检查向量服务是否可用
	if h.svcCtx.VectorService == nil {
		c.JSON(200, result.ErrorVectorService)
		return
	}

	// 设置默认限制
	if request.Limit <= 0 || request.Limit > 100 {
		request.Limit = 10
	}

	// 执行相似搜索
	results, err := h.svcCtx.VectorService.SearchSimilarNews(request.News, request.Limit)
	if err != nil {
		log.Printf("相似搜索失败: %v", err)
		c.JSON(200, result.ErrorVectorService.AddError(err))
		return
	}

	// 返回结果
	response := map[string]interface{}{
		"news":    request.News,
		"limit":   request.Limit,
		"results": results,
		"count":   len(results),
	}

	c.JSON(200, result.SuccessResult(response))
}
