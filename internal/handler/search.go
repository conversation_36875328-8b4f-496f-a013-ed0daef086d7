package handler

import (
	"log"
	"newsBot/internal/result"
	"newsBot/internal/svc"
	"newsBot/internal/types"
	"strconv"

	"github.com/gin-gonic/gin"
)

// SearchHandler 搜索处理器
type SearchHandler struct {
	svcCtx *svc.ServiceContext
}

// NewSearchHandler 创建搜索处理器
func NewSearchHandler(svcCtx *svc.ServiceContext) *SearchHandler {
	return &SearchHandler{
		svcCtx: svcCtx,
	}
}

// SearchByText 文本搜索
func (h *SearchHandler) SearchByText(c *gin.Context) {
	// 获取查询参数
	text := c.Query("text")
	if text == "" {
		result.BadRequest(c, "缺少text参数")
		return
	}

	limitStr := c.Default<PERSON>y("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 100 {
		limit = 10
	}

	// 检查向量服务是否可用
	if h.svcCtx.VectorService == nil {
		result.ServerError(c, "向量搜索服务不可用")
		return
	}

	// 执行搜索
	results, err := h.svcCtx.VectorService.SearchByText(text, limit)
	if err != nil {
		log.Printf("搜索失败: %v", err)
		result.ServerError(c, "搜索失败")
		return
	}

	// 返回结果
	response := map[string]interface{}{
		"query":   text,
		"limit":   limit,
		"results": results,
		"count":   len(results),
	}

	result.Success(c, response)
}

// SearchSimilar 相似搜索
func (h *SearchHandler) SearchSimilar(c *gin.Context) {
	// 解析请求
	var request types.SimilarRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		log.Printf("解析请求失败: %v", err)
		result.BadRequest(c, "请求格式错误")
		return
	}

	// 检查向量服务是否可用
	if h.svcCtx.VectorService == nil {
		result.ServerError(c, "向量搜索服务不可用")
		return
	}

	// 设置默认限制
	if request.Limit <= 0 || request.Limit > 100 {
		request.Limit = 10
	}

	// 执行相似搜索
	results, err := h.svcCtx.VectorService.SearchSimilarNews(request.News, request.Limit)
	if err != nil {
		log.Printf("相似搜索失败: %v", err)
		result.ServerError(c, "相似搜索失败")
		return
	}

	// 返回结果
	response := map[string]interface{}{
		"news":    request.News,
		"limit":   request.Limit,
		"results": results,
		"count":   len(results),
	}

	result.Success(c, response)
}
