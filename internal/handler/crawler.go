package handler

import (
	"fmt"
	"log"
	"newsBot/internal/result"
	"newsBot/internal/svc"
	"newsBot/internal/types"
	"newsBot/internal/utils/crawler"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// CrawlerHandler 新闻爬虫处理器
type CrawlerHandler struct {
	svcCtx *svc.ServiceContext
}

// NewCrawlerHandler 创建新闻爬虫处理器
func NewCrawlerHandler(svcCtx *svc.ServiceContext) *CrawlerHandler {
	return &CrawlerHandler{
		svcCtx: svcCtx,
	}
}

// StartCrawl 启动新闻爬取
func (h *CrawlerHandler) StartCrawl(c *gin.Context) {
	var req types.StartCrawlRequest
	if err := c.ShouldBind(&req); err != nil {
		c.JSON(200, result.ErrorBindingParam.AddError(err))
		return
	}

	log.Printf("🚀 通过API启动新闻爬取，向量化: %v, 演示模式: %v", req.EnableVector, req.DemoMode)

	startTime := time.Now()

	// 执行爬取任务
	response, err := h.runCrawlTask(req.EnableVector && !req.DemoMode)
	if err != nil {
		log.Printf("爬取任务失败: %v", err)
		c.JSON(200, result.ErrorCrawlerService.AddError(err))
		return
	}

	response.Duration = time.Since(startTime).String()

	log.Printf("✅ 新闻爬取完成，共获取 %d 条新闻", response.TotalNews)
	c.JSON(200, result.DataResult("新闻爬取完成", response))
}

// GetCrawlStatus 获取爬取状态
func (h *CrawlerHandler) GetCrawlStatus(c *gin.Context) {
	if h.svcCtx == nil {
		c.JSON(200, result.ErrorServiceNotAvail)
		return
	}

	// 获取最近的爬取会话
	sessions, err := h.svcCtx.CrawlSessionModel.GetCrawlSessions(1)
	if err != nil {
		log.Printf("查询爬取会话失败: %v", err)
		c.JSON(200, result.ErrorSelect.AddError(err))
		return
	}

	if len(sessions) == 0 {
		c.JSON(200, result.SuccessResult(map[string]interface{}{
			"status":  "no_sessions",
			"message": "暂无爬取记录",
		}))
		return
	}

	session := sessions[0]
	status := map[string]interface{}{
		"session_id":    session.Id,
		"status":        session.Status,
		"start_time":    session.StartTime,
		"end_time":      session.EndTime,
		"total_items":   session.TotalNews,
		"success_items": session.SuccessNews,
		"error_message": session.ErrorMsg,
		"created_at":    session.CreatedAt,
	}

	c.JSON(200, result.SuccessResult(status))
}

// HealthCheck 爬虫健康检查
func (h *CrawlerHandler) HealthCheck(c *gin.Context) {
	response := map[string]interface{}{
		"crawler_service": "ok",
		"timestamp":       time.Now().Unix(),
	}

	// 检查向量服务健康状态
	if h.svcCtx.VectorService != nil {
		if err := h.svcCtx.VectorService.HealthCheck(); err != nil {
			response["vector_service"] = "error: " + err.Error()
		} else {
			response["vector_service"] = "ok"
		}
	} else {
		response["vector_service"] = "disabled"
	}

	// 检查数据库状态
	if h.svcCtx != nil && h.svcCtx.DB != nil {
		response["database"] = "ok"
	} else {
		response["database"] = "disabled"
	}

	c.JSON(200, result.SuccessResult(response))
}

// runCrawlTask 执行爬取任务
func (h *CrawlerHandler) runCrawlTask(enableVector bool) (*types.CrawlResponse, error) {
	// 使用GORM数据库连接
	svcCtx := h.svcCtx
	if svcCtx == nil {
		return nil, fmt.Errorf("服务上下文未初始化")
	}

	// 开始爬取会话
	sessionID, err := svcCtx.CrawlSessionModel.StartCrawlSession()
	if err != nil {
		log.Printf("创建爬取会话失败: %v", err)
		// 继续执行，不中断爬取流程
	}

	// 爬取新闻
	allNews := crawler.CrawlWithContentEnhancement()

	if len(allNews) == 0 {
		// 结束会话
		if sessionID > 0 {
			svcCtx.CrawlSessionModel.EndCrawlSession(sessionID, 0, 0, "completed", "")
		}
		return &types.CrawlResponse{
			SessionID:    sessionID,
			TotalNews:    0,
			SuccessCount: 0,
			SourceStats:  make(map[string]int),
			ContentCount: 0,
			CoverageRate: 0,
		}, nil
	}

	// 统计结果
	sourceCount := make(map[string]int)
	contentCount := 0
	successCount := 0

	for _, news := range allNews {
		sourceCount[news.Source]++
		if len(news.Content) > 20 {
			contentCount++
		}

		// 保存到数据库
		if sessionID > 0 {
			if err := svcCtx.NewsRecordModel.SaveNewsRecord(sessionID, news); err != nil {
				log.Printf("保存新闻记录失败: %v", err)
			} else {
				successCount++
			}
		}
	}

	// 向量化处理
	var vectorStats map[string]interface{}
	if enableVector && h.svcCtx.VectorService != nil {
		log.Println("开始向量化处理...")
		startTime := time.Now()

		if err := h.svcCtx.VectorService.ProcessNewsBatch(allNews); err != nil {
			log.Printf("向量化处理失败: %v", err)
		} else {
			duration := time.Since(startTime)
			log.Printf("向量化处理完成，耗时: %v", duration)

			// 获取集合统计信息
			if stats, err := h.svcCtx.VectorService.GetCollectionStats(); err == nil {
				vectorStats = map[string]interface{}{
					"process_duration": duration.String(),
					"collection_stats": stats,
				}
			}
		}
	}

	// 结束爬取会话
	if sessionID > 0 {
		status := "completed"
		errorMsg := ""
		if successCount < len(allNews) {
			status = "partial"
			errorMsg = fmt.Sprintf("部分保存失败: %d/%d", successCount, len(allNews))
		}

		if err := svcCtx.CrawlSessionModel.EndCrawlSession(sessionID, len(allNews), successCount, status, errorMsg); err != nil {
			log.Printf("结束爬取会话失败: %v", err)
		}
	}

	response := &types.CrawlResponse{
		SessionID:    sessionID,
		TotalNews:    len(allNews),
		SuccessCount: successCount,
		SourceStats:  sourceCount,
		ContentCount: contentCount,
		CoverageRate: float64(contentCount) / float64(len(allNews)) * 100,
		VectorStats:  vectorStats,
	}

	return response, nil
}

// CleanOldNews 清理旧新闻
func (h *CrawlerHandler) CleanOldNews(c *gin.Context) {
	daysStr := c.DefaultQuery("days", "30")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days <= 0 {
		result.BadRequest(c, "无效的天数参数")
		return
	}

	if h.svcCtx.VectorService == nil {
		result.ServerError(c, "向量服务不可用")
		return
	}

	if err := h.svcCtx.VectorService.CleanOldNews(days); err != nil {
		log.Printf("清理旧新闻失败: %v", err)
		result.ServerError(c, "清理失败: "+err.Error())
		return
	}

	result.SuccessWithMessage(c, fmt.Sprintf("成功清理%d天前的旧新闻", days), nil)
}
