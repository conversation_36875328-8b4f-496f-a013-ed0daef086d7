package handler

import (
	"log"
	"newsBot/internal/result"
	"newsBot/internal/svc"
	"strconv"

	"github.com/gin-gonic/gin"
)

// HotlistHandler 热榜处理器
type HotlistHandler struct {
	svcCtx *svc.ServiceContext
}

// NewHotlistHandler 创建热榜处理器
func NewHotlistHandler(svcCtx *svc.ServiceContext) *HotlistHandler {
	return &HotlistHandler{
		svcCtx: svcCtx,
	}
}

// GetSessions 获取热搜会话列表
func (h *HotlistHandler) GetSessions(c *gin.Context) {
	if h.svcCtx == nil {
		result.ServerError(c, "数据库服务不可用")
		return
	}

	// 获取查询参数
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 100 {
		limit = 20
	}

	// 查询热搜会话列表
	sessions, err := h.svcCtx.HotlistSessionModel.GetHotlistSessions(limit)
	if err != nil {
		log.Printf("查询热搜会话失败: %v", err)
		result.ServerError(c, "查询热搜会话失败")
		return
	}

	result.Success(c, sessions)
}

// GetRecords 获取指定会话的热搜记录
func (h *HotlistHandler) GetRecords(c *gin.Context) {
	if h.svcCtx == nil {
		result.ServerError(c, "数据库服务不可用")
		return
	}

	// 获取查询参数
	sessionIDStr := c.Query("session_id")
	if sessionIDStr == "" {
		result.BadRequest(c, "缺少session_id参数")
		return
	}

	sessionID, err := strconv.ParseInt(sessionIDStr, 10, 64)
	if err != nil {
		result.BadRequest(c, "无效的session_id")
		return
	}

	limitStr := c.DefaultQuery("limit", "100")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 500 {
		limit = 100
	}

	// 查询热搜记录列表
	records, err := h.svcCtx.HotlistRecordModel.GetHotlistRecordsBySession(sessionID, limit)
	if err != nil {
		log.Printf("查询热搜记录失败: %v", err)
		result.ServerError(c, "查询热搜记录失败")
		return
	}

	result.Success(c, records)
}

// GetStats 获取热搜统计信息
func (h *HotlistHandler) GetStats(c *gin.Context) {
	if h.svcCtx == nil {
		result.ServerError(c, "数据库服务不可用")
		return
	}

	// 获取热搜统计信息
	stats, err := h.svcCtx.HotlistRecordModel.GetHotlistStats()
	if err != nil {
		log.Printf("获取热搜统计失败: %v", err)
		result.ServerError(c, "获取热搜统计失败")
		return
	}

	result.Success(c, stats)
}
