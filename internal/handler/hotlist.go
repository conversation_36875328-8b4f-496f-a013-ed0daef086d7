package handler

import (
	"log"
	"newsBot/internal/result"
	"newsBot/internal/svc"
	"strconv"

	"github.com/gin-gonic/gin"
)

// HotlistHandler 热榜处理器
type HotlistHandler struct {
	svcCtx *svc.ServiceContext
}

// NewHotlistHandler 创建热榜处理器
func NewHotlistHandler(svcCtx *svc.ServiceContext) *HotlistHandler {
	return &HotlistHandler{
		svcCtx: svcCtx,
	}
}

// GetSessions 获取热搜会话列表
func (h *HotlistHandler) GetSessions(c *gin.Context) {
	if h.svcCtx == nil {
		c.JSON(200, result.ErrorServiceNotAvail)
		return
	}

	// 获取查询参数
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 100 {
		limit = 20
	}

	// 查询热搜会话列表
	sessions, err := h.svcCtx.HotlistSessionModel.GetHotlistSessions(limit)
	if err != nil {
		log.Printf("查询热搜会话失败: %v", err)
		c.JSON(200, result.ErrorSelect.AddError(err))
		return
	}

	c.JSON(200, result.SuccessResult(sessions))
}

// GetRecords 获取指定会话的热搜记录
func (h *HotlistHandler) GetRecords(c *gin.Context) {
	if h.svcCtx == nil {
		c.JSON(200, result.ErrorServiceNotAvail)
		return
	}

	// 获取查询参数
	sessionIDStr := c.Query("session_id")
	if sessionIDStr == "" {
		c.JSON(200, result.ErrorReqParam)
		return
	}

	sessionID, err := strconv.ParseInt(sessionIDStr, 10, 64)
	if err != nil {
		c.JSON(200, result.ErrorReqParam.AddError(err))
		return
	}

	limitStr := c.DefaultQuery("limit", "100")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 500 {
		limit = 100
	}

	// 查询热搜记录列表
	records, err := h.svcCtx.HotlistRecordModel.GetHotlistRecordsBySession(sessionID, limit)
	if err != nil {
		log.Printf("查询热搜记录失败: %v", err)
		c.JSON(200, result.ErrorSelect.AddError(err))
		return
	}

	c.JSON(200, result.SuccessResult(records))
}

// GetStats 获取热搜统计信息
func (h *HotlistHandler) GetStats(c *gin.Context) {
	if h.svcCtx == nil {
		c.JSON(200, result.ErrorServiceNotAvail)
		return
	}

	// 获取热搜统计信息
	stats, err := h.svcCtx.HotlistRecordModel.GetHotlistStats()
	if err != nil {
		log.Printf("获取热搜统计失败: %v", err)
		c.JSON(200, result.ErrorSelect.AddError(err))
		return
	}

	c.JSON(200, result.SuccessResult(stats))
}
