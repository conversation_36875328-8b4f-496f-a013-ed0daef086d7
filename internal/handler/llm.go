package handler

import (
	"encoding/json"
	"log"
	"newsBot/internal/result"
	"newsBot/internal/svc"
	"strconv"

	"github.com/gin-gonic/gin"
)

// LLMHandler LLM处理器
type LLMHandler struct {
	svcCtx *svc.ServiceContext
}

// NewLLMHandler 创建LLM处理器
func NewLLMHandler(svcCtx *svc.ServiceContext) *LLMHandler {
	return &LLMHandler{
		svcCtx: svcCtx,
	}
}

// GetTopicAggregationRecords 获取话题合并记录
func (h *LLMHandler) GetTopicAggregationRecords(c *gin.Context) {
	if h.svcCtx == nil {
		result.ServerError(c, "数据库服务不可用")
		return
	}

	// 获取limit参数
	limitStr := c.Default<PERSON>uery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 20
	}

	// 获取话题合并记录
	records, err := h.svcCtx.TopicAggregationModel.GetTopicAggregationRecords(limit)
	if err != nil {
		log.Printf("获取话题合并记录失败: %v", err)
		result.ServerError(c, "获取话题合并记录失败")
		return
	}

	result.Success(c, records)
}

// GetVectorSearchRecords 获取向量搜索记录
func (h *LLMHandler) GetVectorSearchRecords(c *gin.Context) {
	if h.svcCtx == nil {
		result.ServerError(c, "数据库服务不可用")
		return
	}

	// 获取limit参数
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 20
	}

	// 获取向量搜索记录
	records, err := h.svcCtx.VectorSearchModel.GetVectorSearchRecords(limit)
	if err != nil {
		log.Printf("获取向量搜索记录失败: %v", err)
		result.ServerError(c, "获取向量搜索记录失败")
		return
	}

	result.Success(c, records)
}

// GetGeneratedArticleRecords 获取生成文章记录
func (h *LLMHandler) GetGeneratedArticleRecords(c *gin.Context) {
	if h.svcCtx == nil {
		result.ServerError(c, "数据库服务不可用")
		return
	}

	// 获取limit参数
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 20
	}

	// 获取生成文章记录
	records, err := h.svcCtx.GeneratedArticleModel.GetGeneratedArticleRecords(limit)
	if err != nil {
		log.Printf("获取生成文章记录失败: %v", err)
		result.ServerError(c, "获取生成文章记录失败")
		return
	}

	result.Success(c, records)
}

// GetTopicAggregationBySession 根据会话ID获取话题合并记录
func (h *LLMHandler) GetTopicAggregationBySession(c *gin.Context) {
	if h.svcCtx == nil {
		result.ServerError(c, "数据库服务不可用")
		return
	}

	// 获取session_id参数
	sessionIdStr := c.Query("session_id")
	if sessionIdStr == "" {
		result.BadRequest(c, "缺少session_id参数")
		return
	}

	sessionId, err := strconv.ParseInt(sessionIdStr, 10, 64)
	if err != nil {
		result.BadRequest(c, "无效的session_id参数")
		return
	}

	// 获取该会话的话题合并记录
	records, err := h.svcCtx.TopicAggregationModel.GetTopicAggregationRecordsBySession(sessionId)
	if err != nil {
		log.Printf("获取会话话题合并记录失败: %v", err)
		result.ServerError(c, "获取会话话题合并记录失败")
		return
	}

	result.Success(c, records)
}

// GetVectorSearchByTopic 根据话题ID获取向量搜索记录
func (h *LLMHandler) GetVectorSearchByTopic(c *gin.Context) {
	if h.svcCtx == nil {
		result.ServerError(c, "数据库服务不可用")
		return
	}

	// 获取topic_id参数
	topicId := c.Query("topic_id")
	if topicId == "" {
		result.BadRequest(c, "缺少topic_id参数")
		return
	}

	// 获取该话题的向量搜索记录
	records, err := h.svcCtx.VectorSearchModel.GetVectorSearchRecordsByTopic(topicId)
	if err != nil {
		log.Printf("获取话题向量搜索记录失败: %v", err)
		result.ServerError(c, "获取话题向量搜索记录失败")
		return
	}

	result.Success(c, records)
}

// GetGeneratedArticlesByTopic 根据话题ID获取生成文章记录
func (h *LLMHandler) GetGeneratedArticlesByTopic(c *gin.Context) {
	if h.svcCtx == nil {
		result.ServerError(c, "数据库服务不可用")
		return
	}

	// 获取topic_id参数
	topicId := c.Query("topic_id")
	if topicId == "" {
		result.BadRequest(c, "缺少topic_id参数")
		return
	}

	// 获取该话题的生成文章记录
	records, err := h.svcCtx.GeneratedArticleModel.GetGeneratedArticleRecordsByTopic(topicId)
	if err != nil {
		log.Printf("获取话题生成文章记录失败: %v", err)
		result.ServerError(c, "获取话题生成文章记录失败")
		return
	}

	result.Success(c, records)
}

// GetSearchResults 获取搜索结果详情
func (h *LLMHandler) GetSearchResults(c *gin.Context) {
	if h.svcCtx == nil {
		result.ServerError(c, "数据库服务不可用")
		return
	}

	// 获取search_id参数
	searchIdStr := c.Query("search_id")
	if searchIdStr == "" {
		result.BadRequest(c, "缺少search_id参数")
		return
	}

	searchId, err := strconv.ParseInt(searchIdStr, 10, 64)
	if err != nil {
		result.BadRequest(c, "无效的search_id参数")
		return
	}

	// 获取搜索记录
	searchRecord, err := h.svcCtx.VectorSearchModel.GetById(searchId)
	if err != nil {
		log.Printf("获取搜索记录失败: %v", err)
		result.ServerError(c, "获取搜索记录失败")
		return
	}

	if searchRecord == nil {
		result.NotFound(c, "搜索记录不存在")
		return
	}

	// 解析搜索结果URL
	var resultUrls []string
	if searchRecord.ResultURLs != "" {
		if err := json.Unmarshal([]byte(searchRecord.ResultURLs), &resultUrls); err != nil {
			log.Printf("解析搜索结果URL失败: %v", err)
			resultUrls = []string{}
		}
	}

	// 根据URL获取新闻详情
	var newsResults []map[string]interface{}
	for _, url := range resultUrls {
		// 这里可以根据URL查询新闻详情，暂时返回URL
		newsResults = append(newsResults, map[string]interface{}{
			"url":   url,
			"title": "新闻标题", // 实际应该从数据库查询
		})
	}

	response := map[string]interface{}{
		"search_record": searchRecord,
		"results":       newsResults,
		"result_count":  len(newsResults),
	}

	result.Success(c, response)
}

// GetGeneratedArticleDetail 获取生成文章详情
func (h *LLMHandler) GetGeneratedArticleDetail(c *gin.Context) {
	if h.svcCtx == nil {
		result.ServerError(c, "数据库服务不可用")
		return
	}

	// 获取article_id参数
	articleIdStr := c.Query("article_id")
	if articleIdStr == "" {
		result.BadRequest(c, "缺少article_id参数")
		return
	}

	articleId, err := strconv.ParseInt(articleIdStr, 10, 64)
	if err != nil {
		result.BadRequest(c, "无效的article_id参数")
		return
	}

	// 获取文章详情
	article, err := h.svcCtx.GeneratedArticleModel.GetById(articleId)
	if err != nil {
		log.Printf("获取文章详情失败: %v", err)
		result.ServerError(c, "获取文章详情失败")
		return
	}

	if article == nil {
		result.NotFound(c, "文章不存在")
		return
	}

	result.Success(c, article)
}
