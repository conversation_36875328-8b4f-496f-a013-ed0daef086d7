package handler

import (
	"fmt"
	"log"
	"newsBot/internal/model"
	"newsBot/internal/result"
	"newsBot/internal/svc"
	"newsBot/internal/types"
	"newsBot/internal/utils/adapter"
	core2 "newsBot/internal/utils/core"
	hotlist2 "newsBot/internal/utils/hotlist"
	"newsBot/internal/utils/llm"
	"newsBot/internal/utils/notification"
	"time"

	"github.com/gin-gonic/gin"
)

// HotlistGeneratorHandler 热榜生成处理器
type HotlistGeneratorHandler struct {
	svcCtx *svc.ServiceContext
}

// NewHotlistGeneratorHandler 创建热榜生成处理器
func NewHotlistGeneratorHandler(svcCtx *svc.ServiceContext) *HotlistGeneratorHandler {
	return &HotlistGeneratorHandler{
		svcCtx: svcCtx,
	}
}

// StartGeneration 启动热榜内容生成
func (h *HotlistGeneratorHandler) StartGeneration(c *gin.Context) {
	var req types.StartGenerationRequest
	if err := c.ShouldBind(&req); err != nil {
		c.JSON(200, result.ErrorBindingParam.AddError(err))
		return
	}

	log.Printf("🚀 通过API启动热榜内容生成，演示模式: %v", req.DemoMode)

	// 检查配置（演示模式跳过）
	if !req.DemoMode {
		if err := h.checkConfiguration(); err != nil {
			c.JSON(200, result.ErrorReqParam.AddError(err))
			return
		}
	}

	// 初始化系统组件
	orchestrator, err := h.initializeSystem(req.DemoMode)
	if err != nil {
		log.Printf("系统初始化失败: %v", err)
		result.ServerError(c, "系统初始化失败: "+err.Error())
		return
	}

	// 执行工作流
	startTime := time.Now()
	workflowResult := orchestrator.RunDailyWorkflow()
	duration := time.Since(startTime)

	response := &types.GenerationResponse{
		TopicsCount:   workflowResult.TopicsCount,
		ArticlesCount: workflowResult.ArticlesCount,
		Duration:      duration.String(),
		Success:       workflowResult.Success,
		Error:         workflowResult.Error,
	}

	if workflowResult.Success {
		log.Printf("✅ 热榜内容生成完成，话题: %d, 文章: %d", response.TopicsCount, response.ArticlesCount)
		result.SuccessWithMessage(c, "热榜内容生成完成", response)
	} else {
		log.Printf("❌ 热榜内容生成失败: %s", workflowResult.Error)
		result.ServerError(c, "热榜内容生成失败: "+workflowResult.Error)
	}
}

// GetGenerationStatus 获取生成状态
func (h *HotlistGeneratorHandler) GetGenerationStatus(c *gin.Context) {
	if h.svcCtx == nil {
		result.ServerError(c, "数据库服务不可用")
		return
	}

	// 获取最近的热榜会话
	sessions, err := h.svcCtx.HotlistSessionModel.GetHotlistSessions(1)
	if err != nil {
		log.Printf("查询热榜会话失败: %v", err)
		result.ServerError(c, "查询生成状态失败")
		return
	}

	if len(sessions) == 0 {
		result.Success(c, map[string]interface{}{
			"status":  "no_sessions",
			"message": "暂无生成记录",
		})
		return
	}

	session := sessions[0]
	status := map[string]interface{}{
		"session_id":    session.Id,
		"status":        session.Status,
		"start_time":    session.StartTime,
		"end_time":      session.EndTime,
		"total_items":   session.TotalItems,
		"success_items": session.SuccessItems,
		"error_message": session.ErrorMsg,
		"created_at":    session.CreatedAt,
	}

	result.Success(c, status)
}

// HealthCheck 热榜生成健康检查
func (h *HotlistGeneratorHandler) HealthCheck(c *gin.Context) {
	response := map[string]interface{}{
		"hotlist_service": "ok",
		"timestamp":       time.Now().Unix(),
	}

	// 检查配置
	if err := h.checkConfiguration(); err != nil {
		response["config_status"] = "error: " + err.Error()
	} else {
		response["config_status"] = "ok"
	}

	// 检查向量服务健康状态
	if h.svcCtx.VectorService != nil {
		if err := h.svcCtx.VectorService.HealthCheck(); err != nil {
			response["vector_service"] = "error: " + err.Error()
		} else {
			response["vector_service"] = "ok"
		}
	} else {
		response["vector_service"] = "disabled"
	}

	// 检查数据库状态
	if h.svcCtx != nil && h.svcCtx.DB != nil {
		response["database"] = "ok"
	} else {
		response["database"] = "disabled"
	}

	result.Success(c, response)
}

// checkConfiguration 检查配置
func (h *HotlistGeneratorHandler) checkConfiguration() error {
	if h.svcCtx == nil {
		return fmt.Errorf("服务上下文初始化失败")
	}

	cfg := &h.svcCtx.Config

	// 检查Dashscope配置
	if cfg.Dashscope.ApiKey == "" {
		return fmt.Errorf("缺少必需的配置: dashscope.api_key")
	}

	// 检查邮件配置
	if cfg.Email.SmtpHost == "" {
		return fmt.Errorf("缺少必需的配置: email.smtp_host")
	}
	if cfg.Email.SmtpPort == 0 {
		return fmt.Errorf("缺少必需的配置: email.smtp_port")
	}
	if cfg.Email.SmtpUser == "" {
		return fmt.Errorf("缺少必需的配置: email.smtp_user")
	}
	if cfg.Email.SmtpPassword == "" {
		return fmt.Errorf("缺少必需的配置: email.smtp_password")
	}
	if cfg.Email.AdminEmail == "" {
		return fmt.Errorf("缺少必需的配置: email.admin_email")
	}

	return nil
}

// initializeSystem 初始化系统组件
func (h *HotlistGeneratorHandler) initializeSystem(demoMode bool) (*core2.Orchestrator, error) {
	log.Println("🔧 初始化热榜生成系统组件...")

	if h.svcCtx == nil {
		return nil, fmt.Errorf("服务上下文初始化失败")
	}

	// 初始化热榜爬虫
	crawlers := []core2.HotlistCrawler{
		hotlist2.NewWeiboCrawler(),
		hotlist2.NewZhihuCrawler(),
		hotlist2.NewBaiduCrawler(),
	}

	// 初始化LLM客户端
	var llmClient core2.LLMClient
	if demoMode {
		llmClient = &adapter.DemoLLMClient{}
		log.Println("🎭 使用演示模式LLM客户端")
	} else {
		llmClient = llm.NewDashScopeLLMClient()
		log.Println("✅ LLM客户端初始化成功")
	}

	// 初始化通知服务
	var notifier core2.Notifier
	if demoMode {
		notifier = &adapter.DemoNotifier{}
		log.Println("🎭 使用演示模式通知服务")
	} else {
		notifier = notification.NewEmailNotifier()
		log.Println("✅ 邮件通知服务初始化成功")
	}

	// 创建组件
	topicStorage := &adapter.TopicStorageAdapter{Model: model.NewTopicAggregationModel(h.svcCtx.DB)}
	topicAggregator := hotlist2.NewAggregatorService(crawlers, llmClient, topicStorage)
	contentGenerator := &adapter.ContentGenerator{LlmClient: llmClient}
	newsRetriever := &adapter.NewsRetriever{VectorService: h.svcCtx.VectorService}
	articleStorage := &adapter.ArticleStorageAdapter{Model: model.NewGeneratedArticleModel(h.svcCtx.DB)}

	// 创建编排器
	orchestrator := core2.NewOrchestrator(
		topicAggregator,
		contentGenerator,
		notifier,
		newsRetriever,
		topicStorage,
		articleStorage,
	)

	log.Println("🎉 热榜生成系统初始化完成")
	return orchestrator, nil
}
