package svc

import (
	"newsBot/internal/utils/vector"
	"os"
	"path/filepath"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"log"
	"newsBot/internal/config"
	"newsBot/internal/model"
)

// SvcCtx 全局服务上下文（向后兼容，但在新架构中不使用）
var SvcCtx *ServiceContext

type ServiceContext struct {
	Config                config.Config
	DB                    *gorm.DB
	VectorService         *vector.HTTPService
	ProxyModel            *model.ProxyModel
	CrawlSessionModel     *model.CrawlSessionModel
	NewsRecordModel       *model.NewsRecordModel
	HotlistSessionModel   *model.HotlistSessionModel
	HotlistRecordModel    *model.HotlistRecordModel
	TopicAggregationModel *model.TopicAggregationModel
	VectorSearchModel     *model.VectorSearchModel
	GeneratedArticleModel *model.GeneratedArticleModel
}

func NewServiceContext(c config.Config) *ServiceContext {
	// 数据库文件路径
	dbPath := c.Database.Path
	if dbPath == "" {
		// 如果配置中没有指定，使用默认路径
		dataDir := "data"
		if err := os.MkdirAll(dataDir, 0755); err != nil {
			log.Fatalln("创建数据目录失败", "error", err.Error())
		}
		dbPath = filepath.Join(dataDir, "newsbot_gorm.db")
	} else {
		// 确保数据库目录存在
		dbDir := filepath.Dir(dbPath)
		if err := os.MkdirAll(dbDir, 0755); err != nil {
			log.Fatalln("创建数据库目录失败", "error", err.Error())
		}
	}

	db, err := gorm.Open(sqlite.Open(dbPath), &gorm.Config{})
	if err != nil {
		log.Fatalln("连接数据库失败", "error", err.Error())
	}

	// 自动迁移数据表
	if err = autoMigrate(db); err != nil {
		log.Fatalln("数据表迁移失败", "error", err.Error())
	}

	// 初始化向量服务
	var vectorService *vector.HTTPService
	vectorService, err = vector.NewHTTPService()
	if err != nil {
		log.Printf("向量化服务初始化失败: %v", err)
		log.Println("将以传统模式运行（不使用向量化）")
		vectorService = nil
	} else {
		log.Println("✅ 向量化服务初始化成功")
	}

	return &ServiceContext{
		Config:                c,
		DB:                    db,
		VectorService:         vectorService,
		ProxyModel:            model.NewProxyModel(db),
		CrawlSessionModel:     model.NewCrawlSessionModel(db),
		NewsRecordModel:       model.NewNewsRecordModel(db),
		HotlistSessionModel:   model.NewHotlistSessionModel(db),
		HotlistRecordModel:    model.NewHotlistRecordModel(db),
		TopicAggregationModel: model.NewTopicAggregationModel(db),
		VectorSearchModel:     model.NewVectorSearchModel(db),
		GeneratedArticleModel: model.NewGeneratedArticleModel(db),
	}
}

// 自动迁移数据表结构
func autoMigrate(db *gorm.DB) error {
	// 自动迁移所有模型
	return db.AutoMigrate(
		&model.Proxy{},
		&model.CrawlSession{},
		&model.NewsRecord{},
		&model.HotlistSession{},
		&model.HotlistRecord{},
		&model.TopicAggregationRecord{},
		&model.VectorSearchRecord{},
		&model.GeneratedArticleRecord{},
	)
}
