package model

import (
	"time"

	"gorm.io/gorm"
)

// VectorSearchRecord 向量搜索记录
type VectorSearchRecord struct {
	Id          int64     `gorm:"column:id;primaryKey;autoIncrement;comment:数据库主键ID" json:"id"`
	SessionId   int64     `gorm:"column:session_id;default:0;comment:会话ID" json:"session_id"`
	TopicId     string    `gorm:"column:topic_id;not null;comment:话题ID" json:"topic_id"`
	TopicTitle  string    `gorm:"column:topic_title;not null;comment:话题标题" json:"topic_title"`
	SearchQuery string    `gorm:"column:search_query;not null;comment:搜索查询" json:"search_query"`
	ResultCount int       `gorm:"column:result_count;default:0;comment:结果数量" json:"result_count"`
	ResultURLs  string    `gorm:"column:result_urls;comment:结果URL列表JSON" json:"result_urls"`         // JSON格式存储URL列表
	SearchTime  float64   `gorm:"column:search_time;default:0;comment:搜索时间" json:"search_time"`      // 搜索时间（秒）
	Status      string    `gorm:"column:status;not null;default:'success';comment:状态" json:"status"` // success, failed
	ErrorMsg    string    `gorm:"column:error_msg;comment:错误信息" json:"error_msg,omitempty"`
	CreatedAt   time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`
}

// TableName 指定表名
func (VectorSearchRecord) TableName() string {
	return "vector_search_records"
}

// VectorSearchModel 向量搜索记录模型
type VectorSearchModel struct {
	db *gorm.DB
}

// NewVectorSearchModel 创建向量搜索记录模型
func NewVectorSearchModel(db *gorm.DB) *VectorSearchModel {
	return &VectorSearchModel{
		db: db,
	}
}

// Create 创建向量搜索记录
func (m *VectorSearchModel) Create(record *VectorSearchRecord) error {
	return m.db.Create(record).Error
}

// SaveVectorSearchRecord 保存向量搜索记录
func (m *VectorSearchModel) SaveVectorSearchRecord(record VectorSearchRecord) error {
	record.CreatedAt = time.Now()
	return m.db.Create(&record).Error
}

// GetVectorSearchRecords 获取向量搜索记录
func (m *VectorSearchModel) GetVectorSearchRecords(limit int) ([]*VectorSearchRecord, error) {
	var records []*VectorSearchRecord

	db := m.db.Order("created_at DESC")
	if limit > 0 {
		db = db.Limit(limit)
	}

	if err := db.Find(&records).Error; err != nil {
		return nil, err
	}

	return records, nil
}

// GetVectorSearchRecordsByTopic 根据话题ID获取向量搜索记录
func (m *VectorSearchModel) GetVectorSearchRecordsByTopic(topicId string) ([]*VectorSearchRecord, error) {
	var records []*VectorSearchRecord

	if err := m.db.Where("topic_id = ?", topicId).Order("created_at DESC").Find(&records).Error; err != nil {
		return nil, err
	}

	return records, nil
}

// GetById 根据ID获取向量搜索记录
func (m *VectorSearchModel) GetById(id int64) (*VectorSearchRecord, error) {
	var record VectorSearchRecord
	if err := m.db.First(&record, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &record, nil
}

// List 获取向量搜索记录列表（带分页和条件）
func (m *VectorSearchModel) List(page, pageSize int, conditions map[string]interface{}) ([]*VectorSearchRecord, int64, error) {
	var records []*VectorSearchRecord
	var total int64

	db := m.db.Model(&VectorSearchRecord{})

	// 添加查询条件
	for key, value := range conditions {
		if strValue, ok := value.(string); ok && strValue != "" {
			if key == "topic_id" || key == "status" {
				db = db.Where(key+" = ?", value)
			} else if key == "topic_title" || key == "search_query" {
				db = db.Where(key+" LIKE ?", "%"+strValue+"%")
			} else {
				db = db.Where(key+" = ?", value)
			}
		} else if value != nil {
			db = db.Where(key+" = ?", value)
		}
	}

	// 分页查询
	if page > 0 && pageSize > 0 {
		// 获取总数
		if err := db.Count(&total).Error; err != nil {
			return nil, 0, err
		}
		db = db.Offset((page - 1) * pageSize).Limit(pageSize)
	}

	db = db.Order("created_at DESC")
	if err := db.Find(&records).Error; err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

// GetVectorSearchRecordsBySession 根据会话ID获取向量搜索记录
func (m *VectorSearchModel) GetVectorSearchRecordsBySession(sessionId int64) ([]*VectorSearchRecord, error) {
	var records []*VectorSearchRecord

	if err := m.db.Where("session_id = ?", sessionId).Order("created_at DESC").Find(&records).Error; err != nil {
		return nil, err
	}

	return records, nil
}
