package model

import (
	"newsBot/internal/types"
	"time"

	"gorm.io/gorm"
)

// NewsRecord 新闻记录（用于质量检测）
type NewsRecord struct {
	Id            int64     `gorm:"column:id;primaryKey;autoIncrement;comment:数据库主键ID" json:"id"`
	SessionId     int64     `gorm:"column:session_id;not null;comment:会话ID" json:"session_id"`
	Source        string    `gorm:"column:source;not null;comment:新闻来源" json:"source"`
	Title         string    `gorm:"column:title;not null;comment:新闻标题" json:"title"`
	URL           string    `gorm:"column:url;not null;comment:新闻链接" json:"url"`
	Content       string    `gorm:"column:content;comment:新闻内容" json:"content"`
	Summary       string    `gorm:"column:summary;comment:新闻摘要" json:"summary"`
	Author        string    `gorm:"column:author;comment:作者" json:"author"`
	PublishTime   string    `gorm:"column:publish_time;comment:发布时间" json:"publish_time"`
	WordCount     int       `gorm:"column:word_count;default:0;comment:字数" json:"word_count"`
	QualityScore  float64   `gorm:"column:quality_score;default:0;comment:质量评分" json:"quality_score"`    // 质量评分 0-100
	HasContent    bool      `gorm:"column:has_content;default:false;comment:是否有完整内容" json:"has_content"` // 是否有完整内容
	ContentLength int       `gorm:"column:content_length;default:0;comment:内容长度" json:"content_length"`  // 内容长度
	CreatedAt     time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`
}

// TableName 指定表名
func (NewsRecord) TableName() string {
	return "news_records"
}

// NewsRecordModel 新闻记录模型
type NewsRecordModel struct {
	db *gorm.DB
}

// NewNewsRecordModel 创建新闻记录模型
func NewNewsRecordModel(db *gorm.DB) *NewsRecordModel {
	return &NewsRecordModel{
		db: db,
	}
}

// Create 创建新闻记录
func (m *NewsRecordModel) Create(record *NewsRecord) error {
	return m.db.Create(record).Error
}

// SaveNewsRecord 保存新闻记录
func (m *NewsRecordModel) SaveNewsRecord(sessionID int64, news types.NewsItem) error {
	// 计算质量评分
	qualityScore := m.calculateQualityScore(news)

	hasContent := len(news.Content) > 20
	contentLength := len(news.Content)

	record := &NewsRecord{
		SessionId:     sessionID,
		Source:        news.Source,
		Title:         news.Title,
		URL:           news.URL,
		Content:       news.Content,
		Summary:       news.Summary,
		Author:        news.Author,
		PublishTime:   news.PublishTime,
		WordCount:     news.WordCount,
		QualityScore:  qualityScore,
		HasContent:    hasContent,
		ContentLength: contentLength,
		CreatedAt:     time.Now(),
	}

	// 使用 Save 方法实现 INSERT OR REPLACE 的效果
	return m.db.Save(record).Error
}

// calculateQualityScore 计算新闻质量评分
func (m *NewsRecordModel) calculateQualityScore(news types.NewsItem) float64 {
	score := 0.0

	// 标题质量 (20分)
	if len(news.Title) > 10 && len(news.Title) < 100 {
		score += 20
	} else if len(news.Title) > 5 {
		score += 10
	}

	// 内容质量 (40分)
	contentLen := len(news.Content)
	if contentLen > 1000 {
		score += 40
	} else if contentLen > 500 {
		score += 30
	} else if contentLen > 100 {
		score += 20
	} else if contentLen > 20 {
		score += 10
	}

	// 摘要质量 (15分)
	if len(news.Summary) > 50 && len(news.Summary) < 500 {
		score += 15
	} else if len(news.Summary) > 20 {
		score += 10
	}

	// 作者信息 (10分)
	if news.Author != "" {
		score += 10
	}

	// 发布时间 (10分)
	if news.PublishTime != "" {
		score += 10
	}

	// URL有效性 (5分)
	if news.URL != "" && (len(news.URL) > 20) {
		score += 5
	}

	return score
}

// GetNewsRecordsBySession 根据会话ID获取新闻记录
func (m *NewsRecordModel) GetNewsRecordsBySession(sessionID int64, limit int) ([]*NewsRecord, error) {
	var records []*NewsRecord

	db := m.db.Where("session_id = ?", sessionID).Order("created_at DESC")
	if limit > 0 {
		db = db.Limit(limit)
	}

	if err := db.Find(&records).Error; err != nil {
		return nil, err
	}

	return records, nil
}

// GetNewsRecordByID 根据ID获取新闻记录详情
func (m *NewsRecordModel) GetNewsRecordByID(id int64) (*NewsRecord, error) {
	var record NewsRecord
	if err := m.db.First(&record, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &record, nil
}

// GetById 根据ID获取新闻记录
func (m *NewsRecordModel) GetById(id int64) (*NewsRecord, error) {
	return m.GetNewsRecordByID(id)
}

// List 获取新闻记录列表（带分页和条件）
func (m *NewsRecordModel) List(page, pageSize int, conditions map[string]interface{}) ([]*NewsRecord, int64, error) {
	var records []*NewsRecord
	var total int64

	db := m.db.Model(&NewsRecord{})

	// 添加查询条件
	for key, value := range conditions {
		if strValue, ok := value.(string); ok && strValue != "" {
			if key == "source" || key == "title" || key == "author" {
				db = db.Where(key+" LIKE ?", "%"+strValue+"%")
			} else {
				db = db.Where(key+" = ?", value)
			}
		} else if value != nil {
			db = db.Where(key+" = ?", value)
		}
	}

	// 分页查询
	if page > 0 && pageSize > 0 {
		// 获取总数
		if err := db.Count(&total).Error; err != nil {
			return nil, 0, err
		}
		db = db.Offset((page - 1) * pageSize).Limit(pageSize)
	}

	db = db.Order("created_at DESC")
	if err := db.Find(&records).Error; err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

// GetQualityStats 获取质量统计信息
func (m *NewsRecordModel) GetQualityStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 总体统计
	var totalNews int64
	var successNews int64
	var avgQuality float64

	// 获取总数
	if err := m.db.Model(&NewsRecord{}).Count(&totalNews).Error; err != nil {
		return nil, err
	}

	// 获取有内容的数量
	if err := m.db.Model(&NewsRecord{}).Where("has_content = ?", true).Count(&successNews).Error; err != nil {
		return nil, err
	}

	// 获取平均质量分
	if err := m.db.Model(&NewsRecord{}).Select("AVG(quality_score)").Row().Scan(&avgQuality); err != nil {
		avgQuality = 0.0
	}

	successRate := 0.0
	if totalNews > 0 {
		successRate = float64(successNews) / float64(totalNews) * 100
	}

	stats["total_news"] = totalNews
	stats["success_news"] = successNews
	stats["success_rate"] = successRate
	stats["avg_quality"] = avgQuality

	// 按来源统计
	type SourceStat struct {
		Source     string  `json:"source"`
		Count      int64   `json:"count"`
		AvgQuality float64 `json:"avg_quality"`
		AvgLength  float64 `json:"avg_length"`
	}

	var sourceStats []SourceStat
	if err := m.db.Model(&NewsRecord{}).
		Select("source, COUNT(*) as count, AVG(quality_score) as avg_quality, AVG(content_length) as avg_length").
		Group("source").
		Order("count DESC").
		Scan(&sourceStats).Error; err != nil {
		return nil, err
	}

	// 转换为map格式
	var sourceStatsMap []map[string]interface{}
	for _, stat := range sourceStats {
		sourceStatsMap = append(sourceStatsMap, map[string]interface{}{
			"source":      stat.Source,
			"count":       stat.Count,
			"avg_quality": stat.AvgQuality,
			"avg_length":  stat.AvgLength,
		})
	}

	stats["source_stats"] = sourceStatsMap

	// 最近7天的趋势
	type TrendStat struct {
		Date       string  `json:"date"`
		Count      int64   `json:"count"`
		AvgQuality float64 `json:"avg_quality"`
	}

	var trendStats []TrendStat
	if err := m.db.Model(&NewsRecord{}).
		Select("DATE(created_at) as date, COUNT(*) as count, AVG(quality_score) as avg_quality").
		Where("created_at >= datetime('now', '-7 days')").
		Group("DATE(created_at)").
		Order("date DESC").
		Scan(&trendStats).Error; err != nil {
		return nil, err
	}

	// 转换为map格式
	var trendStatsMap []map[string]interface{}
	for _, stat := range trendStats {
		trendStatsMap = append(trendStatsMap, map[string]interface{}{
			"date":        stat.Date,
			"count":       stat.Count,
			"avg_quality": stat.AvgQuality,
		})
	}

	stats["trend_stats"] = trendStatsMap

	return stats, nil
}
