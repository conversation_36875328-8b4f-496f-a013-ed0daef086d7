# NewsBot Frontend

NewsBot 智能新闻聚合系统前端项目，基于 Vue 3 + Element Plus 构建。

## 🚀 技术栈

- **框架**: Vue 3 (Composition API)
- **构建工具**: Vite
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router
- **HTTP客户端**: Axios
- **样式**: SCSS
- **代码规范**: ESLint + Prettier

## 📁 项目结构

```
frontend/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API接口
│   │   ├── index.js       # Axios配置
│   │   └── modules/       # API模块
│   ├── components/        # 公共组件
│   ├── views/            # 页面组件
│   │   ├── Dashboard.vue  # 监控面板
│   │   ├── Workflow.vue   # 工作流监控
│   │   └── Sessions.vue   # 会话历史
│   ├── stores/           # Pinia状态管理
│   ├── router/           # 路由配置
│   ├── utils/            # 工具函数
│   ├── styles/           # 样式文件
│   ├── App.vue           # 根组件
│   └── main.js           # 入口文件
├── package.json
├── vite.config.js        # Vite配置
└── README.md
```

## 🛠️ 开发环境

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖

```bash
cd frontend
npm install
```

### 启动开发服务器

```bash
npm run dev
```

访问 http://localhost:3000

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 🔧 配置说明

### API代理配置

开发环境下，Vite会自动将 `/api` 请求代理到后端服务器 `http://localhost:8081`。

### 环境变量

可以创建以下环境变量文件：

- `.env.local` - 本地环境变量
- `.env.development` - 开发环境变量
- `.env.production` - 生产环境变量

## 📋 功能模块

### 1. 监控面板 (Dashboard)
- 新闻质量统计
- 来源分析图表
- 质量趋势展示

### 2. 工作流监控 (Workflow)
- 热搜抓取流程
- 话题合并状态
- 向量搜索记录
- 文章生成结果

### 3. 会话历史 (Sessions)
- 新闻爬取会话管理
- 工作流历史记录
- 详细数据查看

## 🎨 UI组件

项目使用 Element Plus 作为UI组件库，提供：

- 响应式布局
- 数据表格
- 图表展示
- 对话框
- 分页组件
- 状态标签

## 📱 响应式设计

项目支持多种屏幕尺寸：

- 桌面端 (>= 1200px)
- 平板端 (768px - 1199px)
- 移动端 (< 768px)

## 🔍 代码规范

项目使用 ESLint + Prettier 进行代码规范检查：

```bash
# 检查代码规范
npm run lint

# 自动修复代码格式
npm run lint --fix
```

## 🚀 部署

### 构建静态文件

```bash
npm run build
```

构建完成后，将 `dist` 目录下的文件部署到Web服务器即可。

### Nginx配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://localhost:8081;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 📝 开发指南

### 添加新页面

1. 在 `src/views/` 下创建Vue组件
2. 在 `src/router/index.js` 中添加路由配置
3. 在导航菜单中添加链接

### 添加新API

1. 在 `src/api/modules/` 下创建API模块
2. 使用统一的axios实例进行请求
3. 在组件中导入并使用

### 状态管理

使用Pinia进行状态管理：

```javascript
// 定义store
export const useExampleStore = defineStore('example', () => {
  const data = ref([])
  
  const fetchData = async () => {
    // API调用
  }
  
  return { data, fetchData }
})

// 在组件中使用
const exampleStore = useExampleStore()
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License
