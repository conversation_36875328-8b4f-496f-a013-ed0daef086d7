<template>
  <div id="app">
    <el-container class="layout-container">
      <!-- 顶部导航 -->
      <el-header class="layout-header">
        <div class="header-content">
          <div class="logo">
            <el-icon><DataAnalysis /></el-icon>
            <span>NewsBot</span>
          </div>
          <div class="nav-menu">
            <el-menu
              :default-active="$route.path"
              mode="horizontal"
              router
              background-color="transparent"
              text-color="#fff"
              active-text-color="#409EFF"
            >
              <el-menu-item index="/">
                <el-icon><Monitor /></el-icon>
                <span>监控面板</span>
              </el-menu-item>
              <el-menu-item index="/workflow">
                <el-icon><Connection /></el-icon>
                <span>工作流</span>
              </el-menu-item>
              <el-menu-item index="/sessions">
                <el-icon><List /></el-icon>
                <span>会话历史</span>
              </el-menu-item>
            </el-menu>
          </div>
        </div>
      </el-header>

      <!-- 主要内容区域 -->
      <el-main class="layout-main">
        <router-view />
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { DataAnalysis, Monitor, Connection, List } from '@element-plus/icons-vue'
</script>

<style lang="scss">
#app {
  height: 100vh;
  
  .layout-container {
    height: 100%;
  }
  
  .layout-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 0;
    
    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 100%;
      padding: 0 20px;
      
      .logo {
        display: flex;
        align-items: center;
        color: white;
        font-size: 20px;
        font-weight: bold;
        
        .el-icon {
          margin-right: 8px;
          font-size: 24px;
        }
      }
      
      .nav-menu {
        .el-menu {
          border-bottom: none;
        }
      }
    }
  }
  
  .layout-main {
    background-color: #f5f7fa;
    padding: 20px;
  }
}
</style>
