import { defineStore } from 'pinia'
import { ref } from 'vue'
import { qualityApi } from '@/api/modules/quality'
import { hotlistApi } from '@/api/modules/hotlist'
import { llmApi } from '@/api/modules/llm'

export const useDashboardStore = defineStore('dashboard', () => {
  // 状态
  const stats = ref({})
  const loading = ref(false)
  const workflowCounts = ref({
    hotlist: 0,
    topics: 0,
    search: 0,
    articles: 0
  })

  // 获取统计数据
  const fetchStats = async () => {
    try {
      loading.value = true
      const data = await qualityApi.getStats()
      stats.value = data
    } catch (error) {
      console.error('获取统计数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 更新工作流计数
  const updateWorkflowCounts = async () => {
    try {
      const [hotlistSessions, topicRecords, searchRecords, articles] = await Promise.all([
        hotlistApi.getSessions({ limit: 1 }),
        llmApi.getTopicAggregation({ limit: 1 }),
        llmApi.getVectorSearch({ limit: 10 }),
        llmApi.getGeneratedArticles({ limit: 10 })
      ])

      workflowCounts.value = {
        hotlist: hotlistSessions.length > 0 ? hotlistSessions[0].total_items : 0,
        topics: topicRecords.length > 0 ? JSON.parse(topicRecords[0].output_topics || '[]').length : 0,
        search: searchRecords.length,
        articles: articles.length
      }
    } catch (error) {
      console.error('更新工作流计数失败:', error)
    }
  }

  // 刷新所有数据
  const refreshData = async () => {
    await Promise.all([
      fetchStats(),
      updateWorkflowCounts()
    ])
  }

  return {
    stats,
    loading,
    workflowCounts,
    fetchStats,
    updateWorkflowCounts,
    refreshData
  }
})
