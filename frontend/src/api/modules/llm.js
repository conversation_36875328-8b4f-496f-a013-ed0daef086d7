import api from '../index'

// LLM处理相关API
export const llmApi = {
  // 获取话题聚合记录
  getTopicAggregation(params = {}) {
    return api.get('/llm/topic-aggregation', { params })
  },

  // 获取向量搜索记录
  getVectorSearch(params = {}) {
    return api.get('/llm/vector-search', { params })
  },

  // 获取生成文章记录
  getGeneratedArticles(params = {}) {
    return api.get('/llm/generated-articles', { params })
  },

  // 根据会话ID获取话题聚合记录
  getTopicAggregationBySession(params = {}) {
    return api.get('/llm/topic-aggregation/session', { params })
  },

  // 根据话题ID获取向量搜索记录
  getVectorSearchByTopic(params = {}) {
    return api.get('/llm/vector-search/topic', { params })
  },

  // 根据话题ID获取生成文章记录
  getGeneratedArticlesByTopic(params = {}) {
    return api.get('/llm/generated-articles/topic', { params })
  },

  // 获取搜索结果
  getSearchResults(params = {}) {
    return api.get('/llm/search/results', { params })
  },

  // 获取生成文章详情
  getGeneratedArticleDetail(params = {}) {
    return api.get('/llm/generated-articles/detail', { params })
  }
}
