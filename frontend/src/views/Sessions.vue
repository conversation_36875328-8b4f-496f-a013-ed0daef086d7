<template>
  <div class="sessions">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>会话历史管理</h1>
      <p>查看和管理新闻爬取会话和工作流历史记录</p>
    </div>

    <!-- 标签页 -->
    <el-tabs v-model="activeTab" @tab-change="handleTabChange">
      <!-- 新闻爬取会话 -->
      <el-tab-pane label="新闻爬取" name="news">
        <div class="tab-content">
          <div class="content-header">
            <h2>新闻爬取会话历史</h2>
            <el-button type="primary" @click="refreshNewsSessions" :loading="newsLoading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
          
          <el-table 
            :data="newsSessions" 
            v-loading="newsLoading"
            @row-click="showNewsDetail"
            style="cursor: pointer;"
          >
            <el-table-column prop="id" label="会话ID" width="80" />
            <el-table-column label="开始时间" width="180">
              <template #default="{ row }">
                {{ formatTime(row.start_time) }}
              </template>
            </el-table-column>
            <el-table-column label="结束时间" width="180">
              <template #default="{ row }">
                {{ row.end_time ? formatTime(row.end_time) : '-' }}
              </template>
            </el-table-column>
            <el-table-column label="新闻数量" width="100">
              <template #default="{ row }">
                {{ row.success_news }}/{{ row.total_news }}
              </template>
            </el-table-column>
            <el-table-column label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="错误信息" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.error_msg || '-' }}
              </template>
            </el-table-column>
          </el-table>
          
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="newsPage"
              :page-size="pageSize"
              :total="newsTotalCount"
              layout="total, prev, pager, next"
              @current-change="handleNewsPageChange"
            />
          </div>
        </div>
      </el-tab-pane>

      <!-- 工作流历史 -->
      <el-tab-pane label="工作流历史" name="workflow">
        <div class="tab-content">
          <div class="content-header">
            <h2>热搜工作流历史记录</h2>
            <el-button type="primary" @click="refreshWorkflowSessions" :loading="workflowLoading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
          
          <el-table 
            :data="workflowSessions" 
            v-loading="workflowLoading"
            @row-click="showWorkflowDetail"
            style="cursor: pointer;"
          >
            <el-table-column prop="id" label="会话ID" width="80" />
            <el-table-column label="开始时间" width="180">
              <template #default="{ row }">
                {{ formatTime(row.start_time) }}
              </template>
            </el-table-column>
            <el-table-column label="结束时间" width="180">
              <template #default="{ row }">
                {{ row.end_time ? formatTime(row.end_time) : '-' }}
              </template>
            </el-table-column>
            <el-table-column label="热搜项目" width="100">
              <template #default="{ row }">
                {{ row.success_items }}/{{ row.total_items }}
              </template>
            </el-table-column>
            <el-table-column label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="处理时长" width="120">
              <template #default="{ row }">
                {{ calculateDuration(row.start_time, row.end_time) }}
              </template>
            </el-table-column>
          </el-table>
          
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="workflowPage"
              :page-size="pageSize"
              :total="workflowTotalCount"
              layout="total, prev, pager, next"
              @current-change="handleWorkflowPageChange"
            />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 详情对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="dialogTitle" 
      width="80%"
      :before-close="closeDialog"
    >
      <div class="dialog-content" v-loading="dialogLoading">
        <component :is="dialogComponent" :data="dialogData" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { qualityApi } from '@/api/modules/quality'
import { hotlistApi } from '@/api/modules/hotlist'
import { formatTime, getStatusText, getStatusType } from '@/utils'
import { Refresh } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

// 响应式数据
const activeTab = ref('news')
const pageSize = 20

// 新闻会话数据
const newsSessions = ref([])
const newsLoading = ref(false)
const newsPage = ref(1)
const newsTotalCount = ref(0)

// 工作流会话数据
const workflowSessions = ref([])
const workflowLoading = ref(false)
const workflowPage = ref(1)
const workflowTotalCount = ref(0)

// 对话框数据
const dialogVisible = ref(false)
const dialogTitle = ref('')
const dialogComponent = ref(null)
const dialogData = ref(null)
const dialogLoading = ref(false)

// 加载新闻会话
const loadNewsSessions = async (page = 1) => {
  try {
    newsLoading.value = true
    const data = await qualityApi.getSessions({
      page,
      limit: pageSize
    })
    newsSessions.value = data
    newsTotalCount.value = data.length // 这里应该从API返回总数
  } catch (error) {
    console.error('加载新闻会话失败:', error)
  } finally {
    newsLoading.value = false
  }
}

// 加载工作流会话
const loadWorkflowSessions = async (page = 1) => {
  try {
    workflowLoading.value = true
    const data = await hotlistApi.getSessions({
      page,
      limit: pageSize
    })
    workflowSessions.value = data
    workflowTotalCount.value = data.length // 这里应该从API返回总数
  } catch (error) {
    console.error('加载工作流会话失败:', error)
  } finally {
    workflowLoading.value = false
  }
}

// 刷新新闻会话
const refreshNewsSessions = () => {
  loadNewsSessions(newsPage.value)
}

// 刷新工作流会话
const refreshWorkflowSessions = () => {
  loadWorkflowSessions(workflowPage.value)
}

// 标签页切换
const handleTabChange = (tabName) => {
  if (tabName === 'news' && newsSessions.value.length === 0) {
    loadNewsSessions()
  } else if (tabName === 'workflow' && workflowSessions.value.length === 0) {
    loadWorkflowSessions()
  }
}

// 新闻分页
const handleNewsPageChange = (page) => {
  newsPage.value = page
  loadNewsSessions(page)
}

// 工作流分页
const handleWorkflowPageChange = (page) => {
  workflowPage.value = page
  loadWorkflowSessions(page)
}

// 显示新闻详情
const showNewsDetail = async (row) => {
  try {
    dialogLoading.value = true
    dialogTitle.value = `新闻会话 #${row.id} 详情`
    
    // 获取该会话的新闻列表
    const newsData = await qualityApi.getNews({
      session_id: row.id,
      limit: 100
    })
    
    dialogData.value = {
      session: row,
      news: newsData
    }
    dialogVisible.value = true
  } catch (error) {
    console.error('加载新闻详情失败:', error)
  } finally {
    dialogLoading.value = false
  }
}

// 显示工作流详情
const showWorkflowDetail = async (row) => {
  try {
    dialogLoading.value = true
    dialogTitle.value = `工作流会话 #${row.id} 详情`
    
    // 获取该会话的热榜记录
    const hotlistData = await hotlistApi.getRecords({
      session_id: row.id,
      limit: 100
    })
    
    dialogData.value = {
      session: row,
      records: hotlistData
    }
    dialogVisible.value = true
  } catch (error) {
    console.error('加载工作流详情失败:', error)
  } finally {
    dialogLoading.value = false
  }
}

// 计算持续时间
const calculateDuration = (startTime, endTime) => {
  if (!endTime) return '-'
  const start = dayjs(startTime)
  const end = dayjs(endTime)
  const duration = end.diff(start, 'second')
  
  if (duration < 60) {
    return `${duration}秒`
  } else if (duration < 3600) {
    return `${Math.floor(duration / 60)}分${duration % 60}秒`
  } else {
    const hours = Math.floor(duration / 3600)
    const minutes = Math.floor((duration % 3600) / 60)
    return `${hours}时${minutes}分`
  }
}

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false
  dialogData.value = null
}

// 页面加载时获取数据
onMounted(() => {
  loadNewsSessions()
})
</script>

<style lang="scss" scoped>
.sessions {
  .page-header {
    background: white;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    h1 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
    }
    
    p {
      margin: 0;
      color: #606266;
    }
  }
  
  .tab-content {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    .content-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      h2 {
        margin: 0;
        color: #303133;
        font-size: 18px;
      }
    }
    
    .pagination-wrapper {
      margin-top: 20px;
      text-align: center;
    }
  }
  
  .dialog-content {
    min-height: 300px;
  }
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-table__row) {
  cursor: pointer;
  
  &:hover {
    background-color: #f5f7fa;
  }
}
</style>
