<template>
  <div class="workflow">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>🔥 热搜内容生成工作流</h1>
      <p>监控热搜抓取、话题合并、向量搜索和文章生成的完整流程</p>
      <el-button type="primary" @click="refreshData" :loading="loading">
        <el-icon><Refresh /></el-icon>
        刷新数据
      </el-button>
    </div>

    <!-- 工作流步骤 -->
    <div class="workflow-steps">
      <el-steps :active="4" finish-status="success" align-center>
        <el-step 
          v-for="(step, index) in workflowSteps" 
          :key="index"
          :title="step.title"
          :description="step.description"
          :icon="step.icon"
        />
      </el-steps>
    </div>

    <!-- 工作流详情卡片 -->
    <el-row :gutter="20">
      <el-col :xs="24" :lg="12" v-for="(workflow, index) in workflowData" :key="index">
        <el-card class="workflow-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <div class="header-left">
                <el-icon class="header-icon" :style="{ color: workflow.color }">
                  <component :is="workflow.icon" />
                </el-icon>
                <span>{{ workflow.title }}</span>
              </div>
              <el-badge :value="workflow.count" :type="workflow.badgeType" />
            </div>
          </template>
          
          <div class="workflow-content" v-loading="workflow.loading">
            <div v-if="workflow.data && workflow.data.length > 0">
              <div 
                v-for="(item, idx) in workflow.data.slice(0, 3)" 
                :key="idx"
                class="workflow-item"
                @click="showDetail(workflow.type, item)"
              >
                <div class="item-header">
                  <span class="item-title">{{ getItemTitle(workflow.type, item) }}</span>
                  <el-tag :type="getStatusType(item.status)" size="small">
                    {{ getStatusText(item.status) }}
                  </el-tag>
                </div>
                <div class="item-meta">
                  <span>{{ formatTime(item.created_at, 'MM-DD HH:mm') }}</span>
                  <span v-if="workflow.type === 'hotlist'">{{ item.total_items }}项</span>
                  <span v-if="workflow.type === 'topics'">{{ getTopicCount(item) }}话题</span>
                  <span v-if="workflow.type === 'articles'">{{ item.word_count }}字</span>
                </div>
              </div>
              
              <div class="view-more" v-if="workflow.data.length > 3">
                <el-button type="text" @click="viewMore(workflow.type)">
                  查看更多 ({{ workflow.data.length - 3 }}+)
                </el-button>
              </div>
            </div>
            
            <el-empty v-else description="暂无数据" :image-size="80" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详情对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="dialogTitle" 
      width="80%"
      :before-close="closeDialog"
    >
      <div class="dialog-content" v-loading="dialogLoading">
        <component :is="dialogComponent" :data="dialogData" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { hotlistApi } from '@/api/modules/hotlist'
import { llmApi } from '@/api/modules/llm'
import { formatTime, getStatusText, getStatusType } from '@/utils'
import { 
  Refresh,
  DataAnalysis,
  Connection,
  Search,
  Document
} from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const dialogComponent = ref(null)
const dialogData = ref(null)
const dialogLoading = ref(false)

// 工作流步骤
const workflowSteps = [
  { title: '热搜抓取', description: '从各平台抓取热搜数据', icon: 'DataAnalysis' },
  { title: '话题合并', description: 'LLM智能合并相似话题', icon: 'Connection' },
  { title: '向量搜索', description: '在新闻库中搜索相关内容', icon: 'Search' },
  { title: '文章生成', description: 'LLM生成高质量文章', icon: 'Document' }
]

// 工作流数据
const workflowData = reactive([
  {
    type: 'hotlist',
    title: '热搜抓取',
    icon: 'DataAnalysis',
    color: '#409EFF',
    badgeType: 'primary',
    count: 0,
    data: [],
    loading: false
  },
  {
    type: 'topics',
    title: '话题合并',
    icon: 'Connection',
    color: '#67C23A',
    badgeType: 'success',
    count: 0,
    data: [],
    loading: false
  },
  {
    type: 'search',
    title: '向量搜索',
    icon: 'Search',
    color: '#E6A23C',
    badgeType: 'warning',
    count: 0,
    data: [],
    loading: false
  },
  {
    type: 'articles',
    title: '文章生成',
    icon: 'Document',
    color: '#F56C6C',
    badgeType: 'danger',
    count: 0,
    data: [],
    loading: false
  }
])

// 获取项目标题
const getItemTitle = (type, item) => {
  switch (type) {
    case 'hotlist':
      return `会话 #${item.id}`
    case 'topics':
      return `话题聚合 #${item.id}`
    case 'search':
      return `搜索 #${item.id}`
    case 'articles':
      return item.article_title || `文章 #${item.id}`
    default:
      return `项目 #${item.id}`
  }
}

// 获取话题数量
const getTopicCount = (item) => {
  try {
    return JSON.parse(item.output_topics || '[]').length
  } catch {
    return 0
  }
}

// 加载工作流数据
const loadWorkflowData = async () => {
  try {
    loading.value = true
    
    // 并行加载所有数据
    const [hotlistSessions, topicRecords, searchRecords, articles] = await Promise.all([
      hotlistApi.getSessions({ limit: 10 }),
      llmApi.getTopicAggregation({ limit: 10 }),
      llmApi.getVectorSearch({ limit: 10 }),
      llmApi.getGeneratedArticles({ limit: 10 })
    ])

    // 更新数据
    workflowData[0].data = hotlistSessions
    workflowData[0].count = hotlistSessions.length

    workflowData[1].data = topicRecords
    workflowData[1].count = topicRecords.length

    workflowData[2].data = searchRecords
    workflowData[2].count = searchRecords.length

    workflowData[3].data = articles
    workflowData[3].count = articles.length

  } catch (error) {
    console.error('加载工作流数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = async () => {
  await loadWorkflowData()
}

// 显示详情
const showDetail = (type, item) => {
  dialogData.value = item
  dialogTitle.value = getItemTitle(type, item)
  dialogVisible.value = true
}

// 查看更多
const viewMore = (type) => {
  // 这里可以跳转到专门的列表页面
  console.log('查看更多:', type)
}

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false
  dialogData.value = null
}

// 页面加载时获取数据
onMounted(() => {
  loadWorkflowData()
})
</script>

<style lang="scss" scoped>
.workflow {
  .page-header {
    background: white;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    h1 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
    }
    
    p {
      margin: 0 0 16px 0;
      color: #606266;
    }
  }
  
  .workflow-steps {
    background: white;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
  
  .workflow-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-left {
        display: flex;
        align-items: center;
        
        .header-icon {
          margin-right: 8px;
          font-size: 18px;
        }
      }
    }
    
    .workflow-content {
      min-height: 200px;
      
      .workflow-item {
        padding: 12px;
        border: 1px solid #f0f0f0;
        border-radius: 6px;
        margin-bottom: 8px;
        cursor: pointer;
        transition: all 0.3s;
        
        &:hover {
          border-color: #409EFF;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
        }
        
        .item-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          
          .item-title {
            font-weight: 500;
            color: #303133;
          }
        }
        
        .item-meta {
          display: flex;
          gap: 12px;
          font-size: 12px;
          color: #909399;
        }
      }
      
      .view-more {
        text-align: center;
        margin-top: 12px;
      }
    }
  }
  
  .dialog-content {
    min-height: 300px;
  }
}
</style>
