<template>
  <div class="dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>新闻爬虫质量检测系统</h1>
      <p>实时监控新闻抓取质量和内容完整性</p>
      <el-button type="primary" @click="refreshData" :loading="loading">
        <el-icon><Refresh /></el-icon>
        刷新数据
      </el-button>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6" v-for="(stat, index) in statsCards" :key="index">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon" :style="{ backgroundColor: stat.color }">
              <el-icon><component :is="stat.icon" /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-label">{{ stat.label }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 来源统计图表 -->
    <el-row :gutter="20">
      <el-col :xs="24" :lg="12">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>新闻来源统计</span>
            </div>
          </template>
          <div class="source-stats">
            <div 
              v-for="source in stats.source_stats" 
              :key="source.source"
              class="source-item"
            >
              <div class="source-info">
                <span class="source-name">{{ source.source }}</span>
                <span class="source-count">{{ source.count }}条</span>
              </div>
              <div class="source-metrics">
                <el-tag :type="getQualityType(source.avg_quality)" size="small">
                  质量: {{ source.avg_quality.toFixed(1) }}分
                </el-tag>
                <span class="source-length">平均{{ source.avg_length.toFixed(0) }}字</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :lg="12">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>质量趋势</span>
            </div>
          </template>
          <div class="trend-stats">
            <div 
              v-for="trend in stats.trend_stats" 
              :key="trend.date"
              class="trend-item"
            >
              <div class="trend-date">{{ formatDate(trend.date) }}</div>
              <div class="trend-metrics">
                <div class="trend-quality">
                  <span>平均质量: </span>
                  <el-tag :type="getQualityType(trend.avg_quality)" size="small">
                    {{ trend.avg_quality.toFixed(1) }}分
                  </el-tag>
                </div>
                <div class="trend-count">新闻数量: {{ trend.count }}条</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useDashboardStore } from '@/stores/dashboard'
import { formatTime } from '@/utils'
import { 
  Refresh, 
  Document, 
  SuccessFilled, 
  Warning, 
  TrendCharts 
} from '@element-plus/icons-vue'

const dashboardStore = useDashboardStore()
const { stats, loading } = dashboardStore

// 统计卡片数据
const statsCards = computed(() => [
  {
    label: '总新闻数',
    value: stats.total_news || 0,
    icon: Document,
    color: '#409EFF'
  },
  {
    label: '成功新闻数',
    value: stats.success_news || 0,
    icon: SuccessFilled,
    color: '#67C23A'
  },
  {
    label: '成功率',
    value: `${stats.success_rate || 0}%`,
    icon: TrendCharts,
    color: '#E6A23C'
  },
  {
    label: '平均质量',
    value: stats.avg_quality ? `${stats.avg_quality.toFixed(1)}分` : '0分',
    icon: Warning,
    color: '#F56C6C'
  }
])

// 获取质量等级类型
const getQualityType = (quality) => {
  if (quality >= 90) return 'success'
  if (quality >= 80) return 'warning'
  return 'danger'
}

// 格式化日期
const formatDate = (date) => {
  return formatTime(date, 'MM-DD')
}

// 刷新数据
const refreshData = async () => {
  await dashboardStore.refreshData()
}

// 页面加载时获取数据
onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.dashboard {
  .page-header {
    background: white;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    h1 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
    }
    
    p {
      margin: 0 0 16px 0;
      color: #606266;
    }
  }
  
  .stats-row {
    margin-bottom: 20px;
  }
  
  .stat-card {
    .stat-content {
      display: flex;
      align-items: center;
      
      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        
        .el-icon {
          color: white;
          font-size: 24px;
        }
      }
      
      .stat-info {
        flex: 1;
        
        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
          line-height: 1;
        }
        
        .stat-label {
          color: #909399;
          font-size: 14px;
          margin-top: 4px;
        }
      }
    }
  }
  
  .chart-card {
    height: 300px;
    
    .card-header {
      font-weight: bold;
      color: #303133;
    }
    
    .source-stats, .trend-stats {
      height: 220px;
      overflow-y: auto;
    }
    
    .source-item, .trend-item {
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
    }
    
    .source-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      
      .source-name {
        font-weight: 500;
        color: #303133;
      }
      
      .source-count {
        color: #909399;
        font-size: 14px;
      }
    }
    
    .source-metrics {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .source-length {
        color: #909399;
        font-size: 12px;
      }
    }
    
    .trend-item {
      .trend-date {
        font-weight: 500;
        color: #303133;
        margin-bottom: 8px;
      }
      
      .trend-metrics {
        display: flex;
        flex-direction: column;
        gap: 4px;
        
        .trend-quality, .trend-count {
          font-size: 14px;
          color: #606266;
        }
      }
    }
  }
}
</style>
