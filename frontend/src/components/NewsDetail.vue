<template>
  <div class="news-detail">
    <div v-if="data && data.session" class="session-info">
      <h3>会话信息</h3>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="会话ID">{{ data.session.id }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(data.session.status)">
            {{ getStatusText(data.session.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="开始时间">
          {{ formatTime(data.session.start_time) }}
        </el-descriptions-item>
        <el-descriptions-item label="结束时间">
          {{ data.session.end_time ? formatTime(data.session.end_time) : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="新闻总数">{{ data.session.total_news }}</el-descriptions-item>
        <el-descriptions-item label="成功数量">{{ data.session.success_news }}</el-descriptions-item>
        <el-descriptions-item label="错误信息" :span="2">
          {{ data.session.error_msg || '-' }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <div v-if="data && data.news" class="news-list">
      <h3>新闻列表 ({{ data.news.length }}条)</h3>
      <el-table :data="data.news" max-height="400">
        <el-table-column prop="id" label="ID" width="60" />
        <el-table-column prop="source" label="来源" width="100" />
        <el-table-column prop="title" label="标题" show-overflow-tooltip />
        <el-table-column prop="word_count" label="字数" width="80" />
        <el-table-column prop="quality_score" label="质量分" width="80">
          <template #default="{ row }">
            <el-tag :type="getQualityType(row.quality_score)" size="small">
              {{ row.quality_score }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="发布时间" width="150">
          <template #default="{ row }">
            {{ formatTime(row.publish_time, 'MM-DD HH:mm') }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="viewNews(row)">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 新闻内容对话框 -->
    <el-dialog v-model="newsDialogVisible" title="新闻详情" width="70%">
      <div v-if="selectedNews" class="news-content">
        <h4>{{ selectedNews.title }}</h4>
        <div class="news-meta">
          <span>来源: {{ selectedNews.source }}</span>
          <span>作者: {{ selectedNews.author || '-' }}</span>
          <span>发布时间: {{ formatTime(selectedNews.publish_time) }}</span>
          <span>字数: {{ selectedNews.word_count }}</span>
          <span>质量分: {{ selectedNews.quality_score }}</span>
        </div>
        <div class="news-summary" v-if="selectedNews.summary">
          <h5>摘要</h5>
          <p>{{ selectedNews.summary }}</p>
        </div>
        <div class="news-body">
          <h5>正文</h5>
          <div class="content">{{ selectedNews.content }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { formatTime, getStatusText, getStatusType } from '@/utils'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})

const newsDialogVisible = ref(false)
const selectedNews = ref(null)

// 获取质量等级类型
const getQualityType = (quality) => {
  if (quality >= 90) return 'success'
  if (quality >= 80) return 'warning'
  return 'danger'
}

// 查看新闻详情
const viewNews = (news) => {
  selectedNews.value = news
  newsDialogVisible.value = true
}
</script>

<style lang="scss" scoped>
.news-detail {
  .session-info {
    margin-bottom: 24px;
    
    h3 {
      margin: 0 0 16px 0;
      color: #303133;
    }
  }
  
  .news-list {
    h3 {
      margin: 0 0 16px 0;
      color: #303133;
    }
  }
  
  .news-content {
    .news-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      margin: 16px 0;
      padding: 12px;
      background: #f5f7fa;
      border-radius: 4px;
      font-size: 14px;
      color: #606266;
    }
    
    .news-summary {
      margin: 16px 0;
      
      h5 {
        margin: 0 0 8px 0;
        color: #303133;
      }
      
      p {
        margin: 0;
        padding: 12px;
        background: #f0f8ff;
        border-left: 4px solid #409EFF;
        border-radius: 4px;
        color: #606266;
        line-height: 1.6;
      }
    }
    
    .news-body {
      h5 {
        margin: 16px 0 8px 0;
        color: #303133;
      }
      
      .content {
        max-height: 300px;
        overflow-y: auto;
        padding: 12px;
        background: #fafafa;
        border-radius: 4px;
        line-height: 1.8;
        color: #303133;
        white-space: pre-wrap;
      }
    }
  }
}
</style>
