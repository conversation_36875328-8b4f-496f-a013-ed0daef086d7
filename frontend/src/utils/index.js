import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import relativeTime from 'dayjs/plugin/relativeTime'

dayjs.locale('zh-cn')
dayjs.extend(relativeTime)

// 格式化时间
export const formatTime = (time, format = 'YYYY-MM-DD HH:mm:ss') => {
  if (!time) return '-'
  return dayjs(time).format(format)
}

// 相对时间
export const fromNow = (time) => {
  if (!time) return '-'
  return dayjs(time).fromNow()
}

// 获取状态文本
export const getStatusText = (status) => {
  const statusMap = {
    completed: '已完成',
    failed: '失败',
    partial: '部分成功',
    running: '运行中',
    pending: '等待中',
    success: '成功'
  }
  return statusMap[status] || status
}

// 获取状态类型
export const getStatusType = (status) => {
  const typeMap = {
    completed: 'success',
    failed: 'danger',
    partial: 'warning',
    running: 'primary',
    pending: 'info',
    success: 'success'
  }
  return typeMap[status] || 'info'
}

// 格式化文件大小
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 防抖函数
export const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}
