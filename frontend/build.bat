@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    NewsBot Frontend 项目构建
echo ========================================
echo.

:: 检查Node.js是否安装
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未检测到 Node.js
    pause
    exit /b 1
)

:: 检查依赖是否安装
if not exist "node_modules" (
    echo 📦 正在安装依赖...
    npm install
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

echo 🔨 正在构建生产版本...
echo.

:: 构建项目
npm run build

if errorlevel 1 (
    echo.
    echo ❌ 构建失败
    pause
    exit /b 1
)

echo.
echo ✅ 构建完成！
echo.
echo 📁 构建产物位置: dist/
echo 🌐 可以将 dist 目录部署到Web服务器
echo.
echo 💡 本地预览构建结果:
echo   npm run preview
echo.
pause
