{"name": "newsbot-frontend", "version": "1.0.0", "description": "NewsBot 智能新闻聚合系统前端", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.2", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "dayjs": "^1.11.10"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "vite": "^5.0.8", "sass": "^1.69.5", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "@vue/eslint-config-prettier": "^8.0.0", "prettier": "^3.1.1"}}