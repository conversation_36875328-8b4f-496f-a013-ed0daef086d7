@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    NewsBot Frontend 项目初始化
echo ========================================
echo.

:: 检查Node.js是否安装
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未检测到 Node.js
    echo 请先安装 Node.js (https://nodejs.org/)
    pause
    exit /b 1
)

echo ✅ Node.js 版本:
node --version

:: 检查npm是否可用
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: npm 不可用
    pause
    exit /b 1
)

echo ✅ npm 版本:
npm --version
echo.

:: 安装依赖
echo 📦 正在安装项目依赖...
echo.
npm install

if errorlevel 1 (
    echo.
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

echo.
echo ✅ 依赖安装完成！
echo.
echo 🚀 启动开发服务器...
echo.
echo 📋 可用命令:
echo   npm run dev     - 启动开发服务器
echo   npm run build   - 构建生产版本
echo   npm run preview - 预览生产版本
echo   npm run lint    - 代码规范检查
echo.
echo 🌐 开发服务器将在 http://localhost:3000 启动
echo 📡 API请求将代理到 http://localhost:8081
echo.
echo 按任意键启动开发服务器...
pause >nul

npm run dev
