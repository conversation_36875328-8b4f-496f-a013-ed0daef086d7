# NewsBot Frontend 部署指南

## 🚀 快速开始

### 1. 环境准备

确保已安装：
- Node.js >= 16.0.0
- npm >= 8.0.0

### 2. 项目初始化

```bash
# Windows用户可直接运行
setup.bat

# 或手动执行
cd frontend
npm install
```

### 3. 开发环境启动

```bash
npm run dev
```

访问 http://localhost:3000

## 🔧 配置说明

### API代理配置

开发环境下，前端会自动将API请求代理到后端：

```javascript
// vite.config.js
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:8081',
      changeOrigin: true
    }
  }
}
```

### 后端服务要求

确保后端服务正常运行在 `http://localhost:8081`，并提供以下API端点：

- `GET /api/v1/health` - 健康检查
- `GET /api/v1/quality/stats` - 质量统计
- `GET /api/v1/quality/sessions` - 会话列表
- `GET /api/v1/hotlist/sessions` - 热榜会话
- `GET /api/v1/llm/*` - LLM相关接口

## 🏗️ 生产环境部署

### 1. 构建项目

```bash
# Windows用户可直接运行
build.bat

# 或手动执行
npm run build
```

### 2. 部署静态文件

将 `dist` 目录下的文件部署到Web服务器。

### 3. Nginx配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/frontend/dist;
    index index.html;

    # 前端路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API代理到后端
    location /api {
        proxy_pass http://localhost:8081;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### 4. Apache配置示例

```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /path/to/frontend/dist
    
    # 前端路由支持
    <Directory "/path/to/frontend/dist">
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>
    
    # API代理
    ProxyPass /api http://localhost:8081/api
    ProxyPassReverse /api http://localhost:8081/api
</VirtualHost>
```

## 🐳 Docker部署

### 1. 创建Dockerfile

```dockerfile
# 构建阶段
FROM node:18-alpine as build-stage
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

# 生产阶段
FROM nginx:alpine as production-stage
COPY --from=build-stage /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### 2. 创建nginx.conf

```nginx
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;
        
        location / {
            try_files $uri $uri/ /index.html;
        }
        
        location /api {
            proxy_pass http://backend:8081;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
}
```

### 3. 构建和运行

```bash
# 构建镜像
docker build -t newsbot-frontend .

# 运行容器
docker run -d -p 3000:80 --name newsbot-frontend newsbot-frontend
```

## 🔍 故障排除

### 常见问题

1. **API请求失败**
   - 检查后端服务是否正常运行
   - 确认API端点路径正确
   - 检查CORS配置

2. **页面刷新404**
   - 确保Web服务器配置了前端路由支持
   - 检查 `try_files` 或 `RewriteRule` 配置

3. **静态资源加载失败**
   - 检查资源路径配置
   - 确认构建产物完整

### 调试方法

1. **开发环境调试**
   ```bash
   npm run dev
   ```
   打开浏览器开发者工具查看网络请求

2. **生产环境调试**
   ```bash
   npm run preview
   ```
   本地预览生产构建

3. **API调试**
   - 使用浏览器开发者工具的Network面板
   - 检查API响应状态和数据格式

## 📊 性能优化

### 1. 构建优化

- 代码分割已配置
- 静态资源压缩
- Tree-shaking优化

### 2. 运行时优化

- 组件懒加载
- 图片懒加载
- 虚拟滚动（大数据列表）

### 3. 缓存策略

- 静态资源长期缓存
- API数据适当缓存
- 浏览器缓存利用

## 🔒 安全考虑

1. **API安全**
   - 使用HTTPS
   - 实施CORS策略
   - API访问控制

2. **前端安全**
   - XSS防护
   - CSRF防护
   - 内容安全策略(CSP)

## 📈 监控和日志

### 1. 错误监控

可集成错误监控服务：
- Sentry
- Bugsnag
- LogRocket

### 2. 性能监控

- Web Vitals监控
- 页面加载时间
- API响应时间

### 3. 用户行为分析

- Google Analytics
- 百度统计
- 自定义埋点

## 🔄 持续集成/部署

### GitHub Actions示例

```yaml
name: Deploy Frontend

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
        
    - name: Install dependencies
      run: |
        cd frontend
        npm ci
        
    - name: Build
      run: |
        cd frontend
        npm run build
        
    - name: Deploy
      run: |
        # 部署到服务器的脚本
        rsync -avz frontend/dist/ user@server:/path/to/web/
```

## 📞 技术支持

如遇到部署问题，请：

1. 检查本文档的故障排除部分
2. 查看项目的GitHub Issues
3. 联系技术支持团队
