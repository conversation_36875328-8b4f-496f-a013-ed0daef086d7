# NewsBot 开发环境配置文件

# 向量数据库配置
qdrant:
  host: "localhost"
  port: 6333
  collection_name: "news_collection_dev"
  vector_size: 1536

# 大语言模型配置
dashscope:
  api_key: ""
  embedding_endpoint: "https://dashscope.aliyuncs.com/api/v1/services/embeddings/text-embedding/text-embedding-v1"
  llm_endpoint: "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"

# 邮件配置
email:
  smtp_host: ""
  smtp_port: 587
  smtp_user: ""
  smtp_password: ""
  evernote_email: ""
  admin_email: ""

# 热榜配置
hotlist:
  crawl_interval: "1h"  # 开发环境更频繁
  topic_aggregation_limit: 5
  article_generation_limit: 3

# 爬虫配置
crawler:
  crawl_interval: "30m"  # 开发环境更频繁
  proxy_list: []

# 向量化配置
vector:
  batch_size: 50  # 开发环境较小批次
  max_retries: 2
  cleanup_days: 7  # 开发环境更短保留期

# 日志配置
log:
  level: "debug"  # 开发环境详细日志
  file: "logs/newsbot_dev.log"

# Web服务配置
web:
  port: 8080
  mode: "debug"  # 开发模式
  static_path: "./web"
  
# 数据库配置
database:
  path: "./data/newsbot_dev.db"
