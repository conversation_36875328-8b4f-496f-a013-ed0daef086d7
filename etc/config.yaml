# NewsBot 配置文件

# 向量数据库配置
qdrant:
  host: "localhost"
  port: 6333
  collection_name: "news_collection"
  vector_size: 1536

# 大语言模型配置
dashscope:
  api_key: ""
  embedding_endpoint: "https://dashscope.aliyuncs.com/api/v1/services/embeddings/text-embedding/text-embedding-v1"
  llm_endpoint: "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"

# 邮件配置
email:
  smtp_host: ""
  smtp_port: 587
  smtp_user: ""
  smtp_password: ""
  evernote_email: ""
  admin_email: ""

# 热榜配置
hotlist:
  crawl_interval: "24h"
  topic_aggregation_limit: 10
  article_generation_limit: 5

# 爬虫配置
crawler:
  crawl_interval: "2h"
  proxy_list: []

# 向量化配置
vector:
  batch_size: 100
  max_retries: 3
  cleanup_days: 30

# 日志配置
log:
  level: "info"
  file: "logs/newsbot.log"

# Web服务配置
web:
  port: 8081
  mode: "release"  # debug, release
  static_path: "./web"
  
# 数据库配置
database:
  path: "./data/newsbot_gorm.db"
