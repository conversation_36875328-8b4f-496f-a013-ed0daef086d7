package main

import (
	"flag"
	"fmt"
	"log"
	"newsBot/internal/config"
	"newsBot/internal/router"
	"newsBot/internal/svc"

	"github.com/gin-gonic/gin"
)

var configFile = flag.String("f", "etc/config.yaml", "配置文件路径")

func main() {
	flag.Parse()

	log.Println("🚀 NewsBot Web服务启动...")

	// 加载配置文件
	if err := config.InitConfig(*configFile); err != nil {
		log.Fatalf("加载配置文件失败: %v", err)
	}
	log.Println("✅ 配置文件加载成功")

	c := config.GetConfig()
	if c == nil {
		log.Fatalf("获取配置失败")
	}

	// 设置Gin模式
	if c.Web.Mode != "" {
		gin.SetMode(c.Web.Mode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建Gin引擎
	r := gin.Default()

	// 创建服务上下文
	svcCtx := svc.NewServiceContext(*c)
	log.Println("✅ 服务上下文初始化成功")

	// 设置路由
	router.SetupRouter(r, svcCtx)
	log.Println("✅ 路由设置完成")

	// 确定端口
	port := c.Web.Port
	if port == 0 {
		port = 8080
	}

	log.Printf("🌐 Web服务已启动:")
	log.Printf("- 主页: http://localhost:%d", port)
	log.Printf("- API健康检查: http://localhost:%d/api/v1/health", port)
	log.Printf("- 新闻质量监控: http://localhost:%d/api/v1/quality", port)
	log.Printf("- 热榜工作流监控: http://localhost:%d/api/v1/hotlist", port)
	log.Printf("- 新闻爬虫API: http://localhost:%d/api/v1/crawler", port)
	log.Printf("- 热榜生成API: http://localhost:%d/api/v1/generator", port)
	log.Println("按 Ctrl+C 停止服务")

	// 启动服务器
	if err := r.Run(fmt.Sprintf(":%d", port)); err != nil {
		log.Fatal("服务器启动失败：", err)
	}
}
