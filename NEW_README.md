
## 🏗️ 项目架构

```
dm-assistant/
├── config/                 # 配置文件
│   ├── config.yaml         # 生产环境配置
│   └── config.dev.yaml     # 开发环境配置
├── internal/               # 内部包
│   ├── config/            # 配置管理
│   ├── constant/          # 常量定义
│   ├── customInterface/   # 自定义接口（Kafka、RabbitMQ、Asynq）
│   ├── handler/           # HTTP处理器
│   ├── middleware/        # 中间件（JWT认证等）
│   ├── model/            # 数据模型
│   ├── result/           # 响应结果封装
│   ├── router/           # 路由配置
│   ├── svc/              # 服务上下文
│   ├── types/            # 类型定义
│   └── utils/            # 工具函数
├── main.go               # 程序入口
├── go.mod               # Go模块文件
└── README.md           # 项目说明
```

## 🛠️ 技术栈

- **Web框架**: Gin
- **数据库**: MySQL + GORM
- **定时任务**: Cron

## 📋 环境要求

- Go 1.23.0+
- MySQL 5.7+

## ⚙️ 安装部署






## 🚨 错误处理

系统采用统一的错误处理机制：

```go
// 成功响应
{
  "code": 200,
  "message": "success",
  "data": {...}
}

// 错误响应
{
  "code": 400,
  "message": "error message",
  "data": null
}
```

### 常见错误码
- `200` - 成功
- `400` - 请求参数错误
- `401` - 未授权
- `403` - 权限不足
- `500` - 服务器内部错误



## 📊 监控和日志
- **定时任务日志** - 定时任务执行状态记录

## 🛠️ 开发指南

### 项目结构说明

```
internal/
├── config/           # 配置管理
│   └── config.go    # 配置结构体和加载逻辑
├── constant/        # 业务常量
│   └── businessConstant.go
├── customInterface/ # 自定义接口实现
│   ├── kafka.go    # Kafka消费者实现
│   ├── rabbit_mq.go # RabbitMQ消费者实现
│   └── asynq.go    # Asynq任务队列实现
├── handler/         # HTTP请求处理器
│   ├── user.go     # 用户相关接口
│   ├── account.go  # 账号相关接口
│   ├── card.go     # 卡片相关接口
│   └── ...
├── middleware/      # 中间件
│   └── jwt.go      # JWT认证中间件
├── model/          # 数据模型
│   ├── user.go     # 用户模型
│   ├── account.go  # 账号模型
│   └── ...
├── result/         # 响应结果封装
│   ├── result.go   # 统一响应格式
│   └── error.go    # 错误定义
├── router/         # 路由配置
│   └── router.go   # 路由注册
├── svc/           # 服务上下文
│   └── context.go  # 依赖注入容器
├── types/         # 类型定义
│   └── types.go   # 请求/响应类型
└── utils/         # 工具函数
    ├── jwt.go     # JWT工具
    ├── cron.go    # 定时任务工具
    ├── dy.go      # 抖音API工具
    └── ...
```

### 代码规范

1. **命名规范**
   - 所有形如 `xxxID` 的字段都改为 `xxxId`
   - 使用驼峰命名法
   - 接口名以 `I` 开头

2. **错误处理**
   - 优先使用短变量声明：
   ```go
   if o, err := newObject(); err != nil {
       log.Fatalln("连接数据库失败", "error", err.Error())
   }
   ```

3. **数据库操作**
   - 使用GORM进行数据库操作
   - 所有模型都实现 `TableName()` 方法
   - 使用事务处理复杂业务逻辑

### 添加新功能

1. **添加新的API接口**
   ```go
   // 1. 在 model/ 目录下创建数据模型
   // 2. 在 handler/ 目录下创建处理器
   // 3. 在 router/router.go 中注册路由
   // 4. 在 svc/context.go 中注入依赖
   ```

2. **添加新的消息队列消费者**
   ```go
   // 在 customInterface/ 目录下实现消费者逻辑
   // 在 main.go 中启动消费者
   ```

